import { useState, useEffect, useCallback, useMemo } from 'react';
import { getWaybackItems, getWaybackItemsWithLocalChanges } from '@vannizhang/wayback-core';
import { cacheUtils } from '../utils/historicalImageryCache';

/**
 * Custom hook for managing historical satellite imagery
 * Integrates with ESRI World Imagery Wayback service
 */
export const useHistoricalImagery = (location, enabled = false) => {
  // State for wayback items and loading
  const [waybackItems, setWaybackItems] = useState([]);
  const [availableItems, setAvailableItems] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isHistoricalMode, setIsHistoricalMode] = useState(false);

  // Performance optimization: debounce location changes
  const [debouncedLocation, setDebouncedLocation] = useState(location);

  /**
   * Load all available wayback items with caching
   */
  const loadWaybackItems = useCallback(async () => {
    if (!enabled) return;

    try {
      setIsLoading(true);
      setError(null);

      // Check cache first
      const cachedItems = cacheUtils.getCachedWaybackItems();
      if (cachedItems) {
        setWaybackItems(cachedItems);
        setIsLoading(false);
        return;
      }

      console.log('[useHistoricalImagery] Loading wayback items...');
      const items = await getWaybackItems();

      // Sort by release date (newest first)
      const sortedItems = items.sort((a, b) => b.releaseDatetime - a.releaseDatetime);

      setWaybackItems(sortedItems);

      // Cache the results
      cacheUtils.setCachedWaybackItems(sortedItems);

      console.log(`[useHistoricalImagery] Loaded ${sortedItems.length} wayback items:`,
        sortedItems.slice(0, 3).map(item => ({
          date: item.releaseDateLabel,
          releaseNum: item.releaseNum
        }))
      );
    } catch (err) {
      console.error('[useHistoricalImagery] Error loading wayback items:', err);
      setError(`Failed to load historical imagery: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [enabled]);

  /**
   * Load wayback items with local changes for a specific location with caching
   */
  const loadLocalChanges = useCallback(async (lat, lng, zoom = 15) => {
    if (!enabled || !lat || !lng) return;

    try {
      setIsLoading(true);
      setError(null);

      // Check cache first
      const cachedItems = cacheUtils.getCachedLocalChanges(lat, lng, zoom);
      if (cachedItems) {
        setAvailableItems(cachedItems);
        setIsLoading(false);
        return;
      }

      console.log(`[useHistoricalImagery] Loading local changes for ${lat}, ${lng} at zoom ${zoom}...`);
      const items = await getWaybackItemsWithLocalChanges(
        { latitude: lat, longitude: lng },
        zoom
      );

      // Sort by release date (newest first)
      const sortedItems = items.sort((a, b) => b.releaseDatetime - a.releaseDatetime);

      setAvailableItems(sortedItems);

      // Cache the results
      cacheUtils.setCachedLocalChanges(lat, lng, zoom, sortedItems);

      console.log(`[useHistoricalImagery] Found ${sortedItems.length} items with local changes:`,
        sortedItems.slice(0, 3).map(item => ({
          date: item.releaseDateLabel,
          releaseNum: item.releaseNum
        }))
      );
    } catch (err) {
      console.error('[useHistoricalImagery] Error loading local changes:', err);
      setError(`Failed to load location-specific imagery: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [enabled]);

  /**
   * Select a specific wayback item and automatically enable historical mode
   */
  const selectWaybackItem = useCallback((item) => {
    console.log('[useHistoricalImagery] Selecting wayback item:', {
      date: item?.releaseDateLabel || 'current',
      releaseNum: item?.releaseNum,
      itemURL: item?.itemURL
    });
    setSelectedItem(item);
    setIsHistoricalMode(!!item);
  }, []);

  /**
   * Reset to current imagery
   */
  const resetToCurrent = useCallback(() => {
    setSelectedItem(null);
    setIsHistoricalMode(false);
    console.log('[useHistoricalImagery] Reset to current imagery');
  }, []);

  /**
   * Get tile URL for the selected wayback item
   * Converts ESRI Wayback URL format to Leaflet-compatible format
   */
  const getTileUrl = useCallback((item = selectedItem) => {
    if (!item) return null;

    // Replace {releaseNum} placeholder with actual release number
    // and convert ESRI format {level}/{row}/{col} to Leaflet format {z}/{y}/{x}
    return item.itemURL
      .replace('{releaseNum}', item.releaseNum)
      .replace('{level}', '{z}')
      .replace('{row}', '{y}')
      .replace('{col}', '{x}');
  }, [selectedItem]);

  /**
   * Format date for display
   */
  const formatDate = useCallback((timestamp) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }, []);

  /**
   * Get date range of available imagery
   */
  const dateRange = useMemo(() => {
    const items = availableItems.length > 0 ? availableItems : waybackItems;
    if (items.length === 0) return null;

    const dates = items.map(item => item.releaseDatetime).sort((a, b) => a - b);
    return {
      earliest: dates[0],
      latest: dates[dates.length - 1],
      count: items.length
    };
  }, [availableItems, waybackItems]);

  /**
   * Debounce location changes to avoid excessive API calls
   */
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedLocation(location);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [location]);

  /**
   * Load items when debounced location changes
   */
  useEffect(() => {
    if (enabled && debouncedLocation?.lat && debouncedLocation?.lng) {
      loadLocalChanges(debouncedLocation.lat, debouncedLocation.lng);
    }
  }, [enabled, debouncedLocation?.lat, debouncedLocation?.lng, loadLocalChanges]);

  /**
   * Load all items when enabled
   */
  useEffect(() => {
    if (enabled) {
      loadWaybackItems();
    }
  }, [enabled, loadWaybackItems]);

  return {
    // Data
    waybackItems,
    availableItems,
    selectedItem,
    dateRange,
    
    // State
    isLoading,
    error,
    isHistoricalMode,
    
    // Actions
    loadWaybackItems,
    loadLocalChanges,
    selectWaybackItem,
    resetToCurrent,
    getTileUrl,
    formatDate,
    
    // Utilities
    setIsHistoricalMode: (enabled) => {
      setIsHistoricalMode(enabled);
      if (!enabled) {
        setSelectedItem(null);
      }
    }
  };
};

export default useHistoricalImagery;
