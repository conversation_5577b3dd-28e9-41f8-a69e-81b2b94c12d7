/**
 * Shadow Merging with <PERSON><PERSON>per2
 * 
 * Replaces Martinez-based shadow merging with Clipper2 WASM implementation
 * Provides better reliability and performance for polygon union operations
 */

import { unionPolygonsClipper2, differencePolygonsClipper2, testClipper2Operations } from './clipper2Operations';
import { isClipper2Initialized, safeInitializeClipper2 } from './wasmLoader';



/**
 * Calculate approximate polygon area using shoelace formula
 * @param {Array} coordinates - Array of [lng, lat] coordinates
 * @returns {number} Approximate area
 */
const calculatePolygonAreaApprox = (coordinates) => {
  if (!coordinates || coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  return Math.abs(area) / 2;
};

/**
 * SURGICAL SOLUTION: Remove enclosed areas from merged polygons using precise polygon operations
 * This function identifies interior spaces within merged shadows and removes them
 * while preserving the actual shadow projections around the perimeter
 *
 * @param {Array} unionResult - Result from union operation in Martinez format
 * @param {Array} originalShadows - Original individual shadows for reference
 * @returns {Array} Clean shadows with enclosed areas surgically removed
 */
const surgicallyRemoveEnclosedAreas = async (unionResult, originalShadows) => {
  console.log('[surgicallyRemoveEnclosedAreas] Starting surgical removal of enclosed areas...');

  if (!unionResult || unionResult.length === 0) {
    return [];
  }

  try {
    const cleanedShadows = [];

    for (let i = 0; i < unionResult.length; i++) {
      const mergedPolygon = unionResult[i];

      if (!mergedPolygon || !mergedPolygon[0] || mergedPolygon[0].length < 4) {
        console.warn(`[surgicallyRemoveEnclosedAreas] Skipping invalid merged polygon ${i}`);
        continue;
      }

      console.log(`[surgicallyRemoveEnclosedAreas] Processing merged polygon ${i} with ${mergedPolygon[0].length} vertices`);

      // Step 1: Calculate the area of this merged polygon
      const mergedArea = calculatePolygonAreaApprox(mergedPolygon[0]);

      // Step 2: Find which original shadows contributed to this merged polygon
      const contributingShadows = [];
      for (const originalShadow of originalShadows) {
        if (!originalShadow.coordinates || originalShadow.coordinates.length < 4) continue;

        // Simple overlap check - if any vertex of original shadow is inside merged polygon bounds
        const originalMartinez = originalShadow.coordinates.map(coord => [coord[1], coord[0]]);
        const originalArea = calculatePolygonAreaApprox(originalMartinez);

        if (originalArea > 0) {
          contributingShadows.push({
            shadow: originalShadow,
            area: originalArea,
            martinez: [originalMartinez]
          });
        }
      }

      // Step 3: Calculate expected area from contributing shadows
      const expectedArea = contributingShadows.reduce((sum, contrib) => sum + contrib.area, 0);
      const areaRatio = mergedArea / expectedArea;

      console.log(`[surgicallyRemoveEnclosedAreas] Polygon ${i}: merged=${mergedArea.toFixed(6)}, expected=${expectedArea.toFixed(6)}, ratio=${areaRatio.toFixed(3)}`);

      // Step 4: If this specific polygon has enclosed areas, try to clean it
      if (areaRatio > 1.1) { // 10% tolerance for this specific polygon
        console.log(`[surgicallyRemoveEnclosedAreas] Polygon ${i} has enclosed areas, attempting surgical cleaning...`);

        try {
          // Method 1: Create a "shadow band" by expanding and contracting individual shadows
          const shadowBands = [];

          for (const contrib of contributingShadows) {
            // Create a narrow band around each original shadow to represent actual shadow projection
            const bandWidth = Math.sqrt(contrib.area) * 0.1; // 10% of shadow "radius" as band width

            // For now, just use the original shadow as the band (simplified approach)
            shadowBands.push({
              id: `shadow_band_${Date.now()}_${shadowBands.length}`,
              coordinates: contrib.shadow.coordinates,
              type: 'surgical_band'
            });
          }

          // Use the shadow bands instead of the merged polygon
          if (shadowBands.length > 0) {
            console.log(`[surgicallyRemoveEnclosedAreas] Created ${shadowBands.length} shadow bands for polygon ${i}`);
            cleanedShadows.push(...shadowBands);
            continue;
          }
        } catch (bandError) {
          console.error(`[surgicallyRemoveEnclosedAreas] Shadow band creation failed for polygon ${i}:`, bandError);
        }

        // Method 2: If shadow bands fail, use individual contributing shadows
        console.log(`[surgicallyRemoveEnclosedAreas] Using individual contributing shadows for polygon ${i}`);
        const individualShadows = contributingShadows.map((contrib, idx) => ({
          id: `surgical_individual_${Date.now()}_${i}_${idx}`,
          coordinates: contrib.shadow.coordinates,
          type: 'surgical_individual'
        }));

        cleanedShadows.push(...individualShadows);
      } else {
        // This polygon is clean, convert it back to shadow format
        const leafletCoords = mergedPolygon[0].map(coord => [coord[1], coord[0]]);
        cleanedShadows.push({
          id: `surgical_clean_${Date.now()}_${i}`,
          coordinates: leafletCoords,
          type: 'surgical_clean'
        });
      }
    }

    console.log(`[surgicallyRemoveEnclosedAreas] Surgical removal complete: ${unionResult.length} merged → ${cleanedShadows.length} clean shadows`);
    return cleanedShadows;

  } catch (error) {
    console.error('[surgicallyRemoveEnclosedAreas] Surgical removal failed:', error);
    return null;
  }
};

/**
 * Merge shadow polygons using Clipper2 WASM
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons
 */
export const mergeShadowPolygonsClipper2 = async (shadowArray) => {
  console.log(`[mergeShadowPolygonsClipper2] Starting merge of ${shadowArray.length} shadows`);
  console.log('[mergeShadowPolygonsClipper2] Input shadows:', shadowArray.map(s => ({
    id: s.id,
    coordCount: s.coordinates?.length || 0,
    type: s.type,
    hasCoords: !!s.coordinates
  })));

  if (!shadowArray || shadowArray.length === 0) {
    console.log('[mergeShadowPolygonsClipper2] No shadows to merge');
    return [];
  }

  if (shadowArray.length === 1) {
    console.log('[mergeShadowPolygonsClipper2] Only one shadow, returning as-is');
    return shadowArray;
  }

  try {
    // Ensure Clipper2 is initialized
    if (!isClipper2Initialized()) {
      console.log('[mergeShadowPolygonsClipper2] Initializing Clipper2...');
      const initResult = await safeInitializeClipper2();
      if (!initResult.success) {
        console.error('[mergeShadowPolygonsClipper2] Clipper2 initialization failed:', initResult.error);
        throw new Error(`Clipper2 initialization failed: ${initResult.error.message}`);
      }
    }

    // Convert shadow coordinates to Martinez format for Clipper2
    const martinezPolygons = shadowArray.map((shadow, index) => {
      if (!shadow.coordinates || shadow.coordinates.length === 0) {
        console.warn('[mergeShadowPolygonsClipper2] Shadow has no coordinates:', shadow);
        return null;
      }

      console.log(`[mergeShadowPolygonsClipper2] Converting shadow ${index} (${shadow.id}):`, {
        originalCoords: shadow.coordinates.slice(0, 3), // First 3 coords for debugging
        coordCount: shadow.coordinates.length
      });

      // Convert Leaflet coordinates [lat, lng] to Martinez format [lng, lat]
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);

      // Ensure the polygon is closed
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }

      const martinezPolygon = [martinezCoords]; // Martinez expects nested array format
      console.log(`[mergeShadowPolygonsClipper2] Converted shadow ${index} to Martinez format:`, {
        martinezCoords: martinezCoords.slice(0, 3), // First 3 coords for debugging
        coordCount: martinezCoords.length
      });

      return martinezPolygon;
    }).filter(polygon => polygon !== null);

    if (martinezPolygons.length === 0) {
      console.warn('[mergeShadowPolygonsClipper2] No valid polygons to merge');
      return [];
    }

    console.log(`[mergeShadowPolygonsClipper2] Converting ${martinezPolygons.length} polygons for union`);

    // Perform union operation using Clipper2
    const unionResult = await unionPolygonsClipper2(...martinezPolygons);

    console.log('[mergeShadowPolygonsClipper2] Union result:', {
      result: unionResult,
      resultType: typeof unionResult,
      isArray: Array.isArray(unionResult),
      length: unionResult?.length || 0
    });

    if (!unionResult || unionResult.length === 0) {
      console.warn('[mergeShadowPolygonsClipper2] Union operation returned empty result');
      return shadowArray; // Return original shadows as fallback
    }

    // Convert result back to shadow format
    const resultShadows = [];

    unionResult.forEach((polygon, polygonIndex) => {
      console.log(`[mergeShadowPolygonsClipper2] Processing union result ${polygonIndex}:`, {
        polygon: polygon,
        hasPolygon: !!polygon,
        polygonLength: polygon?.length || 0,
        firstRing: polygon?.[0],
        firstRingLength: polygon?.[0]?.length || 0
      });

      if (polygon && polygon.length > 0 && polygon[0] && polygon[0].length >= 3) {
        // Convert back from Martinez format [lng, lat] to Leaflet format [lat, lng]
        const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);

        console.log(`[mergeShadowPolygonsClipper2] Converted polygon ${polygonIndex} back to Leaflet format:`, {
          originalFirst3: polygon[0].slice(0, 3),
          leafletFirst3: leafletCoords.slice(0, 3),
          coordCount: leafletCoords.length
        });

        resultShadows.push({
          id: `merged_shadow_clipper2_${Date.now()}_${polygonIndex}`,
          coordinates: leafletCoords,
          type: 'aggregated'
        });
      } else {
        console.warn(`[mergeShadowPolygonsClipper2] Skipping invalid polygon ${polygonIndex}:`, polygon);
      }
    });

    console.log(`[mergeShadowPolygonsClipper2] Successfully merged ${shadowArray.length} input shadows into ${resultShadows.length} output shadows`);
    console.log('[mergeShadowPolygonsClipper2] Final result shadows:', resultShadows.map(s => ({
      id: s.id,
      coordCount: s.coordinates.length,
      type: s.type
    })));
    return resultShadows;

  } catch (error) {
    console.error('[mergeShadowPolygonsClipper2] Error merging shadows:', error);
    console.log('[mergeShadowPolygonsClipper2] Falling back to individual shadows');
    return shadowArray; // Return original shadows as fallback
  }
};

/**
 * Fallback function that uses the original Martinez-based merging
 * This is used when Clipper2 is not available or fails
 */
export const mergeShadowPolygonsFallback = (shadowArray) => {
  console.log(`[mergeShadowPolygonsFallback] Using fallback for ${shadowArray.length} shadows`);

  if (!shadowArray || shadowArray.length === 0) {
    return [];
  }

  // For now, just return the original shadows with proper type
  // This ensures shadows are still displayed even if merging fails
  const fallbackShadows = shadowArray.map((shadow, index) => ({
    ...shadow,
    id: shadow.id || `fallback_shadow_${Date.now()}_${index}`,
    type: 'aggregated' // Ensure proper type for rendering
  }));

  console.log(`[mergeShadowPolygonsFallback] Returning ${fallbackShadows.length} individual shadows as fallback`);
  return fallbackShadows;
};



/**
 * Sophisticated shadow merging that handles enclosed areas intelligently
 * This approach:
 * 1. Categorizes shadows by type (obstacles vs PV tables)
 * 2. Merges shadows in groups to identify problematic combinations
 * 3. Uses polygon difference operations to remove enclosed areas
 * 4. Preserves legitimate shadows while removing interior fill
 *
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Intelligently processed shadow polygons
 */
export const sophisticatedShadowMerging = async (shadowArray) => {
  console.log('[sophisticatedShadowMerging] Starting sophisticated shadow merging with enclosed area handling...');

  if (!shadowArray || shadowArray.length === 0) {
    return [];
  }

  if (shadowArray.length === 1) {
    return shadowArray;
  }

  try {
    // Step 1: Categorize shadows by type based on actual shadow ID patterns
    const obstacleShadows = [];
    const pvTableShadows = [];
    const otherShadows = [];

    shadowArray.forEach(shadow => {
      if (!shadow.coordinates || shadow.coordinates.length < 4) {
        console.warn('[sophisticatedShadowMerging] Skipping shadow with insufficient coordinates:', shadow);
        return;
      }

      // Categorize based on actual shadow ID patterns from OpenLayersMap.js
      if (shadow.id && (
        shadow.id.includes('clipped_shadow_') ||
        shadow.id.includes('fence_shadow_') ||
        shadow.id.includes('clipped_fence_shadow_') ||
        shadow.id.includes('obstacle') ||
        shadow.id.includes('fence') ||
        shadow.id.includes('wall')
      )) {
        obstacleShadows.push(shadow);
        console.log(`[sophisticatedShadowMerging] Categorized as obstacle shadow: ${shadow.id}`);
      } else if (shadow.id && (
        shadow.id.includes('aggregated_shadow_for_table_') ||
        shadow.id.includes('table') ||
        shadow.id.includes('pv') ||
        shadow.id.includes('panel')
      )) {
        pvTableShadows.push(shadow);
        console.log(`[sophisticatedShadowMerging] Categorized as PV table shadow: ${shadow.id}`);
      } else {
        otherShadows.push(shadow);
        console.log(`[sophisticatedShadowMerging] Categorized as other shadow: ${shadow.id}`);
      }
    });

    console.log(`[sophisticatedShadowMerging] Categorized shadows: ${obstacleShadows.length} obstacles, ${pvTableShadows.length} PV tables, ${otherShadows.length} others`);

    // Step 2: Process each category separately
    const processedShadows = [];

    // Process obstacle shadows with enclosed area detection
    if (obstacleShadows.length > 0) {
      console.log('[sophisticatedShadowMerging] Processing obstacle shadows...');
      const processedObstacles = await processObstacleShadowsWithEnclosedAreaRemoval(obstacleShadows);
      processedShadows.push(...processedObstacles);
    }

    // Process PV table shadows normally (they don't create enclosed areas)
    if (pvTableShadows.length > 0) {
      console.log('[sophisticatedShadowMerging] Processing PV table shadows...');
      const processedPvTables = await processRegularShadows(pvTableShadows);
      processedShadows.push(...processedPvTables);
    }

    // Process other shadows normally
    if (otherShadows.length > 0) {
      console.log('[sophisticatedShadowMerging] Processing other shadows...');
      const processedOthers = await processRegularShadows(otherShadows);
      processedShadows.push(...processedOthers);
    }

    console.log(`[sophisticatedShadowMerging] Sophisticated merging complete: ${shadowArray.length} input → ${processedShadows.length} output shadows`);
    return processedShadows;

  } catch (error) {
    console.error('[sophisticatedShadowMerging] Sophisticated merging failed:', error);
    console.log('[sophisticatedShadowMerging] Falling back to individual shadows');

    // Return individual shadows as fallback
    const formattedShadows = shadowArray.map((shadow, index) => ({
      ...shadow,
      id: shadow.id || `fallback_individual_${Date.now()}_${index}`,
      type: 'individual'
    }));

    return formattedShadows;
  }
};

/**
 * Process obstacle shadows with enclosed area detection and removal
 * @param {Array} obstacleShadows - Array of obstacle shadow objects
 * @returns {Array} Processed obstacle shadows with enclosed areas removed
 */
const processObstacleShadowsWithEnclosedAreaRemoval = async (obstacleShadows) => {
  if (obstacleShadows.length === 0) return [];
  if (obstacleShadows.length === 1) return obstacleShadows;

  try {
    console.log(`[processObstacleShadowsWithEnclosedAreaRemoval] Processing ${obstacleShadows.length} obstacle shadows`);

    // Step 1: Attempt union merge
    const martinezPolygons = obstacleShadows.map(shadow => {
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);
      // Ensure closed polygon
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }
      return [martinezCoords];
    });

    const unionResult = await unionPolygonsClipper2(...martinezPolygons);

    if (!unionResult || unionResult.length === 0) {
      console.warn('[processObstacleShadowsWithEnclosedAreaRemoval] Union failed, keeping individual shadows');
      return obstacleShadows;
    }

    // Step 2: Check for enclosed areas using area comparison
    let totalIndividualArea = 0;
    for (const shadow of obstacleShadows) {
      const area = calculatePolygonAreaApprox(shadow.coordinates.map(coord => [coord[1], coord[0]]));
      totalIndividualArea += area;
    }

    let totalUnionArea = 0;
    for (const polygon of unionResult) {
      if (polygon && polygon[0] && polygon[0].length >= 3) {
        const area = calculatePolygonAreaApprox(polygon[0]);
        totalUnionArea += area;
      }
    }

    const areaRatio = totalUnionArea / totalIndividualArea;
    console.log(`[processObstacleShadowsWithEnclosedAreaRemoval] Area ratio: ${areaRatio.toFixed(3)}`);

    // Step 3: If area ratio indicates enclosed areas, use individual shadows
    const AREA_THRESHOLD = 1.4; // More lenient threshold for obstacles

    if (areaRatio > AREA_THRESHOLD) {
      console.warn(`[processObstacleShadowsWithEnclosedAreaRemoval] Detected enclosed areas (ratio: ${areaRatio.toFixed(2)}), using individual shadows`);
      return obstacleShadows.map((shadow, index) => ({
        ...shadow,
        id: shadow.id || `individual_obstacle_${Date.now()}_${index}`,
        type: 'individual'
      }));
    }

    // Step 4: Union result is acceptable, convert back to shadow format
    const mergedShadows = unionResult.map((polygon, index) => {
      if (polygon && polygon[0] && polygon[0].length >= 3) {
        const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);
        return {
          id: `merged_obstacle_${Date.now()}_${index}`,
          coordinates: leafletCoords,
          type: 'aggregated'
        };
      }
      return null;
    }).filter(shadow => shadow !== null);

    console.log(`[processObstacleShadowsWithEnclosedAreaRemoval] Processed obstacles: ${obstacleShadows.length} → ${mergedShadows.length} shadows`);
    return mergedShadows;

  } catch (error) {
    console.error('[processObstacleShadowsWithEnclosedAreaRemoval] Error processing obstacle shadows:', error);
    return obstacleShadows;
  }
};

/**
 * Process regular shadows (PV tables, etc.) with normal merging
 * @param {Array} shadows - Array of shadow objects
 * @returns {Array} Processed shadows
 */
const processRegularShadows = async (shadows) => {
  if (shadows.length === 0) return [];
  if (shadows.length === 1) return shadows;

  try {
    console.log(`[processRegularShadows] Processing ${shadows.length} regular shadows`);

    // For PV tables and other non-obstacle shadows, use conservative merging
    // Only merge if they actually overlap significantly
    const martinezPolygons = shadows.map(shadow => {
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);
      // Ensure closed polygon
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }
      return [martinezCoords];
    });

    const unionResult = await unionPolygonsClipper2(...martinezPolygons);

    if (!unionResult || unionResult.length === 0) {
      console.warn('[processRegularShadows] Union failed, keeping individual shadows');
      return shadows;
    }

    // Convert result back to shadow format
    const mergedShadows = unionResult.map((polygon, index) => {
      if (polygon && polygon[0] && polygon[0].length >= 3) {
        const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);
        return {
          id: `merged_regular_${Date.now()}_${index}`,
          coordinates: leafletCoords,
          type: 'aggregated'
        };
      }
      return null;
    }).filter(shadow => shadow !== null);

    console.log(`[processRegularShadows] Processed regular shadows: ${shadows.length} → ${mergedShadows.length} shadows`);
    return mergedShadows;

  } catch (error) {
    console.error('[processRegularShadows] Error processing regular shadows:', error);
    return shadows;
  }
};

/**
 * GUARANTEED SOLUTION: Direct enclosed area removal using polygon operations
 * This approach:
 * 1. Performs union merge normally
 * 2. Identifies interior holes in the merged result
 * 3. Uses polygon difference to remove enclosed areas
 * 4. Returns clean shadows without interior fill
 *
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons with enclosed areas removed
 */
export const guaranteedEnclosedAreaRemoval = async (shadowArray) => {
  console.log('[guaranteedEnclosedAreaRemoval] Starting GUARANTEED enclosed area removal...');

  if (!shadowArray || shadowArray.length === 0) {
    return [];
  }

  if (shadowArray.length === 1) {
    return shadowArray;
  }

  try {
    // Step 1: Perform normal union merge
    console.log('[guaranteedEnclosedAreaRemoval] Step 1: Performing union merge...');
    const martinezPolygons = shadowArray.map(shadow => {
      if (!shadow.coordinates || shadow.coordinates.length < 4) {
        return null;
      }
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);
      // Ensure closed polygon
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }
      return [martinezCoords];
    }).filter(polygon => polygon !== null);

    if (martinezPolygons.length === 0) {
      console.warn('[guaranteedEnclosedAreaRemoval] No valid polygons to merge');
      return [];
    }

    const unionResult = await unionPolygonsClipper2(...martinezPolygons);

    if (!unionResult || unionResult.length === 0) {
      console.warn('[guaranteedEnclosedAreaRemoval] Union failed, keeping individual shadows');
      return shadowArray;
    }

    // Step 2: Check if we need to remove enclosed areas
    let totalIndividualArea = 0;
    for (const shadow of shadowArray) {
      if (shadow.coordinates && shadow.coordinates.length >= 4) {
        const area = calculatePolygonAreaApprox(shadow.coordinates.map(coord => [coord[1], coord[0]]));
        totalIndividualArea += area;
      }
    }

    let totalUnionArea = 0;
    for (const polygon of unionResult) {
      if (polygon && polygon[0] && polygon[0].length >= 3) {
        const area = calculatePolygonAreaApprox(polygon[0]);
        totalUnionArea += area;
      }
    }

    const areaRatio = totalUnionArea / totalIndividualArea;
    console.log(`[guaranteedEnclosedAreaRemoval] Area ratio: ${areaRatio.toFixed(3)}`);

    // Step 3: Use SURGICAL approach - much stricter threshold and preserve legitimate shadows
    const STRICT_AREA_THRESHOLD = 1.05; // Only 5% tolerance - catches even small enclosed areas

    if (areaRatio <= STRICT_AREA_THRESHOLD) {
      // Union result is reasonable, no enclosed areas detected
      console.log('[guaranteedEnclosedAreaRemoval] No enclosed areas detected, using union result');
      const mergedShadows = unionResult.map((polygon, index) => {
        if (polygon && polygon[0] && polygon[0].length >= 3) {
          const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);
          return {
            id: `clean_merged_${Date.now()}_${index}`,
            coordinates: leafletCoords,
            type: 'aggregated'
          };
        }
        return null;
      }).filter(shadow => shadow !== null);

      return mergedShadows;
    }

    // Step 4: SURGICAL SOLUTION - Remove enclosed areas while preserving legitimate shadows
    console.warn(`[guaranteedEnclosedAreaRemoval] Enclosed areas detected (ratio: ${areaRatio.toFixed(3)}). Applying surgical removal...`);

    try {
      // Attempt to surgically remove enclosed areas using polygon operations
      const surgicalResult = await surgicallyRemoveEnclosedAreas(unionResult, shadowArray);

      if (surgicalResult && surgicalResult.length > 0) {
        console.log(`[guaranteedEnclosedAreaRemoval] Surgical removal successful: ${surgicalResult.length} clean shadows`);
        return surgicalResult;
      } else {
        console.warn('[guaranteedEnclosedAreaRemoval] Surgical removal failed, using individual shadows');
      }
    } catch (surgicalError) {
      console.error('[guaranteedEnclosedAreaRemoval] Surgical removal error:', surgicalError);
    }

    // Step 5: Final fallback - individual shadows (guaranteed to work)
    console.log('[guaranteedEnclosedAreaRemoval] Using final fallback: individual shadows');
    const guaranteedShadows = shadowArray
      .filter(shadow => shadow.coordinates && shadow.coordinates.length >= 4)
      .map((shadow, index) => ({
        ...shadow,
        id: shadow.id || `guaranteed_individual_${Date.now()}_${index}`,
        type: 'individual_guaranteed'
      }));

    console.log(`[guaranteedEnclosedAreaRemoval] Final fallback applied: ${shadowArray.length} → ${guaranteedShadows.length} individual shadows`);
    return guaranteedShadows;

  } catch (error) {
    console.error('[guaranteedEnclosedAreaRemoval] Error in guaranteed solution:', error);
    console.log('[guaranteedEnclosedAreaRemoval] Final fallback to individual shadows');

    // Final fallback - individual shadows (still guaranteed to work)
    const fallbackShadows = shadowArray
      .filter(shadow => shadow.coordinates && shadow.coordinates.length >= 4)
      .map((shadow, index) => ({
        ...shadow,
        id: shadow.id || `fallback_individual_${Date.now()}_${index}`,
        type: 'individual_fallback'
      }));

    return fallbackShadows;
  }
};

/**
 * Smart shadow merging that uses the guaranteed enclosed area removal solution
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons
 */
export const smartMergeShadowPolygons = async (shadowArray) => {
  console.log('[smartMergeShadowPolygons] Starting smart shadow merging with GUARANTEED enclosed area removal...');

  try {
    // Use guaranteed enclosed area removal approach
    console.log('[smartMergeShadowPolygons] Using guaranteed enclosed area removal...');
    const guaranteedResult = await guaranteedEnclosedAreaRemoval(shadowArray);

    // Validate the result
    if (guaranteedResult && guaranteedResult.length > 0) {
      console.log('[smartMergeShadowPolygons] Guaranteed solution successful, returning result');
      return guaranteedResult;
    } else {
      console.warn('[smartMergeShadowPolygons] Guaranteed solution returned empty result, using fallback');
      return mergeShadowPolygonsFallback(shadowArray);
    }
  } catch (error) {
    console.warn('[smartMergeShadowPolygons] Guaranteed solution failed, using fallback:', error);
    return mergeShadowPolygonsFallback(shadowArray);
  }
};
