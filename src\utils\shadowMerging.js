/**
 * Shadow Merging with <PERSON><PERSON>per2
 * 
 * Replaces Martinez-based shadow merging with Clipper2 WASM implementation
 * Provides better reliability and performance for polygon union operations
 */

import { unionPolygonsClipper2, differencePolygonsClipper2, testClipper2Operations } from './clipper2Operations';
import { isClipper2Initialized, safeInitializeClipper2, getClipper2Instance } from './wasmLoader';
import * as martinez from 'martinez-polygon-clipping';

/**
 * Calculate the area of a polygon using the shoelace formula
 * @param {Array} coordinates - Array of [lng, lat] coordinates
 * @returns {number} Area in square meters (approximate)
 */
const calculatePolygonArea = (coordinates) => {
  if (!coordinates || coordinates.length < 3) {
    return 0;
  }

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  return Math.abs(area) / 2;
};

/**
 * Identify enclosed areas within a merged shadow polygon
 * These are areas that are completely surrounded by shadow boundaries but should remain non-shaded
 * @param {Array} mergedPolygon - Merged shadow polygon in Martinez format
 * @param {Array} originalShadows - Array of original individual shadow objects
 * @returns {Array} Array of enclosed area polygons in Martinez format
 */
const identifyEnclosedAreas = async (mergedPolygon, originalShadows) => {
  console.log(`[identifyEnclosedAreas] Analyzing merged polygon against ${originalShadows.length} original shadows`);

  if (!mergedPolygon || !mergedPolygon[0] || mergedPolygon[0].length < 3) {
    return [];
  }

  try {
    const enclosedAreas = [];

    // Strategy: Find areas within the merged polygon that are not covered by any original shadow
    // These represent interior spaces that should not be shaded

    // Create a grid of test points within the merged polygon bounds
    const bounds = calculateBounds(mergedPolygon[0]);
    const testPoints = generateTestPoints(bounds, 20); // Generate a 20x20 grid of test points

    console.log(`[identifyEnclosedAreas] Generated ${testPoints.length} test points within bounds`);

    // Find points that are inside the merged polygon but not inside any original shadow
    const interiorPoints = [];

    for (const point of testPoints) {
      // Check if point is inside the merged polygon
      if (isPointInPolygon(point, mergedPolygon[0])) {
        // Check if point is NOT inside any original shadow
        let insideOriginalShadow = false;

        for (const originalShadow of originalShadows) {
          if (originalShadow.coordinates && originalShadow.coordinates.length >= 3) {
            // Convert original shadow coordinates to Martinez format
            const originalCoords = originalShadow.coordinates.map(coord => [coord[1], coord[0]]);

            if (isPointInPolygon(point, originalCoords)) {
              insideOriginalShadow = true;
              break;
            }
          }
        }

        if (!insideOriginalShadow) {
          interiorPoints.push(point);
        }
      }
    }

    console.log(`[identifyEnclosedAreas] Found ${interiorPoints.length} interior points not covered by original shadows`);

    // If we found interior points, create enclosed areas around them
    if (interiorPoints.length > 0) {
      // Group nearby interior points and create polygons around them
      const enclosedPolygons = createEnclosedPolygonsFromPoints(interiorPoints, bounds);
      enclosedAreas.push(...enclosedPolygons);
    }

    console.log(`[identifyEnclosedAreas] Identified ${enclosedAreas.length} enclosed areas`);
    return enclosedAreas;

  } catch (error) {
    console.error('[identifyEnclosedAreas] Error identifying enclosed areas:', error);
    return [];
  }
};

/**
 * Calculate bounding box for a polygon
 * @param {Array} coordinates - Array of [lng, lat] coordinates
 * @returns {Object} Bounding box {minLng, minLat, maxLng, maxLat}
 */
const calculateBounds = (coordinates) => {
  if (!coordinates || coordinates.length === 0) {
    return { minLng: 0, minLat: 0, maxLng: 0, maxLat: 0 };
  }

  let minLng = coordinates[0][0];
  let maxLng = coordinates[0][0];
  let minLat = coordinates[0][1];
  let maxLat = coordinates[0][1];

  for (const coord of coordinates) {
    minLng = Math.min(minLng, coord[0]);
    maxLng = Math.max(maxLng, coord[0]);
    minLat = Math.min(minLat, coord[1]);
    maxLat = Math.max(maxLat, coord[1]);
  }

  return { minLng, minLat, maxLng, maxLat };
};

/**
 * Generate a grid of test points within the given bounds
 * @param {Object} bounds - Bounding box {minLng, minLat, maxLng, maxLat}
 * @param {number} gridSize - Number of points per side (creates gridSize x gridSize grid)
 * @returns {Array} Array of [lng, lat] test points
 */
const generateTestPoints = (bounds, gridSize) => {
  const points = [];
  const lngStep = (bounds.maxLng - bounds.minLng) / (gridSize - 1);
  const latStep = (bounds.maxLat - bounds.minLat) / (gridSize - 1);

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const lng = bounds.minLng + i * lngStep;
      const lat = bounds.minLat + j * latStep;
      points.push([lng, lat]);
    }
  }

  return points;
};

/**
 * Check if a point is inside a polygon using ray casting algorithm
 * @param {Array} point - [lng, lat] coordinates
 * @param {Array} polygon - Array of [lng, lat] coordinates
 * @returns {boolean} True if point is inside polygon
 */
const isPointInPolygon = (point, polygon) => {
  if (!point || !polygon || polygon.length < 3) {
    return false;
  }

  const [x, y] = point;
  let inside = false;

  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];

    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }

  return inside;
};

/**
 * Create enclosed polygons from interior points
 * @param {Array} interiorPoints - Array of [lng, lat] points that are in interior areas
 * @param {Object} bounds - Bounding box of the merged polygon
 * @returns {Array} Array of enclosed area polygons in Martinez format
 */
const createEnclosedPolygonsFromPoints = (interiorPoints, bounds) => {
  if (interiorPoints.length === 0) {
    return [];
  }

  // For simplicity, create rectangular areas around clusters of interior points
  // In a more sophisticated implementation, we could use clustering algorithms
  // and create more precise polygon shapes

  const enclosedPolygons = [];
  const bufferSize = Math.min(
    (bounds.maxLng - bounds.minLng) / 50,
    (bounds.maxLat - bounds.minLat) / 50
  );

  // Group nearby points and create rectangles around them
  const processedPoints = new Set();

  for (let i = 0; i < interiorPoints.length; i++) {
    if (processedPoints.has(i)) continue;

    const centerPoint = interiorPoints[i];
    const nearbyPoints = [centerPoint];
    processedPoints.add(i);

    // Find nearby points
    for (let j = i + 1; j < interiorPoints.length; j++) {
      if (processedPoints.has(j)) continue;

      const distance = Math.sqrt(
        Math.pow(interiorPoints[j][0] - centerPoint[0], 2) +
        Math.pow(interiorPoints[j][1] - centerPoint[1], 2)
      );

      if (distance < bufferSize * 3) {
        nearbyPoints.push(interiorPoints[j]);
        processedPoints.add(j);
      }
    }

    if (nearbyPoints.length >= 2) {
      // Create a rectangular enclosed area around these points
      const pointBounds = calculateBounds(nearbyPoints);

      const enclosedRect = [
        [pointBounds.minLng - bufferSize, pointBounds.minLat - bufferSize],
        [pointBounds.maxLng + bufferSize, pointBounds.minLat - bufferSize],
        [pointBounds.maxLng + bufferSize, pointBounds.maxLat + bufferSize],
        [pointBounds.minLng - bufferSize, pointBounds.maxLat + bufferSize],
        [pointBounds.minLng - bufferSize, pointBounds.minLat - bufferSize]
      ];

      enclosedPolygons.push(enclosedRect);
    }
  }

  return enclosedPolygons;
};

/**
 * Specialized union operation for shadow merging that preserves interior areas
 * Uses EvenOdd fill rule to better handle enclosed spaces
 * @param {...Array} polygons - Polygons in Martinez format
 * @returns {Array} Result in Martinez format with interior areas preserved
 */
const unionShadowsWithInteriorPreservation = async (...polygons) => {
  console.log(`[unionShadowsWithInteriorPreservation] Union operation with ${polygons.length} polygons using EvenOdd fill rule`);

  try {
    // Ensure Clipper2 is initialized
    if (!isClipper2Initialized()) {
      const initResult = await safeInitializeClipper2();
      if (!initResult.success) {
        throw new Error(`Clipper2 initialization failed: ${initResult.error.message}`);
      }
    }

    const clipper2 = getClipper2Instance();
    if (!clipper2) {
      throw new Error('Clipper2 WASM instance not available');
    }

    if (polygons.length === 0) {
      return [];
    }

    if (polygons.length === 1) {
      return polygons[0];
    }

    // Convert coordinates to Clipper2 format (reusing logic from clipper2Operations.js)
    const COORDINATE_SCALE = 1000000;

    const convertToClipper2PathsD = (martinezCoords) => {
      const pathsD = new clipper2.PathsD();

      martinezCoords.forEach((ring) => {
        if (!ring || !Array.isArray(ring)) return;

        const pathD = new clipper2.PathD();
        ring.forEach((coord) => {
          if (!coord || !Array.isArray(coord) || coord.length < 2) return;

          const scaledX = coord[0] * COORDINATE_SCALE;
          const scaledY = coord[1] * COORDINATE_SCALE;
          const pointD = new clipper2.PointD(scaledX, scaledY, 0);
          pathD.push_back(pointD);
        });

        if (pathD.size() >= 3) {
          pathsD.push_back(pathD);
        } else {
          pathD.delete();
        }
      });

      return pathsD;
    };

    const convertFromClipper2PathsD = (pathsD) => {
      const result = [];
      const pathCount = pathsD.size();

      for (let i = 0; i < pathCount; i++) {
        const pathD = pathsD.get(i);
        const pointCount = pathD.size();
        const ring = [];

        for (let j = 0; j < pointCount; j++) {
          const pointD = pathD.get(j);
          const originalX = pointD.x / COORDINATE_SCALE;
          const originalY = pointD.y / COORDINATE_SCALE;
          ring.push([originalX, originalY]);
        }

        if (ring.length > 0) {
          result.push([ring]);
        }
      }

      return result;
    };

    // Convert first polygon to subject
    let subjectPaths = convertToClipper2PathsD(polygons[0]);

    // Union with remaining polygons one by one using EvenOdd fill rule
    for (let i = 1; i < polygons.length; i++) {
      const clipPaths = convertToClipper2PathsD(polygons[i]);

      // Use EvenOdd fill rule instead of NonZero to better handle interior areas
      console.log(`[unionShadowsWithInteriorPreservation] Performing union operation ${i} with EvenOdd fill rule`);
      const tempResult = clipper2.UnionD(subjectPaths, clipPaths, clipper2.FillRule.EvenOdd, 2);

      // Clean up previous subject paths
      if (subjectPaths) {
        subjectPaths.delete();
      }

      subjectPaths = tempResult;

      // Clean up clip paths
      if (clipPaths) {
        clipPaths.delete();
      }
    }

    // Convert result back to Martinez format
    const martinezResult = convertFromClipper2PathsD(subjectPaths);

    // Clean up
    if (subjectPaths) {
      subjectPaths.delete();
    }

    console.log(`[unionShadowsWithInteriorPreservation] Union completed with EvenOdd rule, result has ${martinezResult.length} parts`);
    return martinezResult;

  } catch (error) {
    console.error('[unionShadowsWithInteriorPreservation] Union operation failed:', error);
    // Fallback to regular union
    console.log('[unionShadowsWithInteriorPreservation] Falling back to regular union operation');
    return await unionPolygonsClipper2(...polygons);
  }
};

/**
 * Alternative shadow merging approach that preserves non-shaded interior areas
 * Uses a different strategy to avoid creating filled interior spaces
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons that preserve interior spaces
 */
const mergeWithInteriorPreservation = async (shadowArray) => {
  console.log(`[mergeWithInteriorPreservation] Alternative merging for ${shadowArray.length} shadows`);

  if (!shadowArray || shadowArray.length === 0) {
    return [];
  }

  if (shadowArray.length === 1) {
    return shadowArray;
  }

  try {
    // Strategy: Instead of using a simple union, we'll use a more sophisticated approach
    // that identifies and preserves interior non-shaded areas

    // Convert shadow coordinates to Martinez format for Clipper2
    const martinezPolygons = shadowArray.map((shadow, index) => {
      if (!shadow.coordinates || shadow.coordinates.length === 0) {
        return null;
      }

      // Convert Leaflet coordinates [lat, lng] to Martinez format [lng, lat]
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);

      // Ensure the polygon is closed
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }

      return [martinezCoords]; // Martinez expects nested array format
    }).filter(polygon => polygon !== null);

    if (martinezPolygons.length === 0) {
      return [];
    }

    // Use specialized union operation that preserves interior areas
    const unionResult = await unionShadowsWithInteriorPreservation(...martinezPolygons);

    if (!unionResult || unionResult.length === 0) {
      return shadowArray; // Fallback to original shadows
    }

    // Convert result back to shadow format
    const resultShadows = [];
    unionResult.forEach((polygon, polygonIndex) => {
      if (polygon && polygon.length > 0 && polygon[0] && polygon[0].length >= 3) {
        // Convert back from Martinez format [lng, lat] to Leaflet format [lat, lng]
        const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);

        resultShadows.push({
          id: `preserved_shadow_${Date.now()}_${polygonIndex}`,
          coordinates: leafletCoords,
          type: 'aggregated'
        });
      }
    });

    return resultShadows;

  } catch (error) {
    console.error('[mergeWithInteriorPreservation] Error in alternative merging:', error);
    return shadowArray; // Return original shadows as fallback
  }
};



/**
 * Merge shadow polygons using Clipper2 WASM with enclosed area removal
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons with enclosed areas removed
 */
export const mergeShadowPolygonsClipper2 = async (shadowArray) => {
  console.log(`[mergeShadowPolygonsClipper2] Starting merge of ${shadowArray.length} shadows with enclosed area removal`);
  console.log('[mergeShadowPolygonsClipper2] Input shadows:', shadowArray.map(s => ({
    id: s.id,
    coordCount: s.coordinates?.length || 0,
    type: s.type,
    hasCoords: !!s.coordinates
  })));

  if (!shadowArray || shadowArray.length === 0) {
    console.log('[mergeShadowPolygonsClipper2] No shadows to merge');
    return [];
  }

  if (shadowArray.length === 1) {
    console.log('[mergeShadowPolygonsClipper2] Only one shadow, returning as-is');
    return shadowArray;
  }

  try {
    // Ensure Clipper2 is initialized
    if (!isClipper2Initialized()) {
      console.log('[mergeShadowPolygonsClipper2] Initializing Clipper2...');
      const initResult = await safeInitializeClipper2();
      if (!initResult.success) {
        console.error('[mergeShadowPolygonsClipper2] Clipper2 initialization failed:', initResult.error);
        throw new Error(`Clipper2 initialization failed: ${initResult.error.message}`);
      }
    }

    // Convert shadow coordinates to Martinez format for Clipper2
    const martinezPolygons = shadowArray.map((shadow, index) => {
      if (!shadow.coordinates || shadow.coordinates.length === 0) {
        console.warn('[mergeShadowPolygonsClipper2] Shadow has no coordinates:', shadow);
        return null;
      }

      console.log(`[mergeShadowPolygonsClipper2] Converting shadow ${index} (${shadow.id}):`, {
        originalCoords: shadow.coordinates.slice(0, 3), // First 3 coords for debugging
        coordCount: shadow.coordinates.length
      });

      // Convert Leaflet coordinates [lat, lng] to Martinez format [lng, lat]
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);

      // Ensure the polygon is closed
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }

      const martinezPolygon = [martinezCoords]; // Martinez expects nested array format
      console.log(`[mergeShadowPolygonsClipper2] Converted shadow ${index} to Martinez format:`, {
        martinezCoords: martinezCoords.slice(0, 3), // First 3 coords for debugging
        coordCount: martinezCoords.length
      });

      return martinezPolygon;
    }).filter(polygon => polygon !== null);

    if (martinezPolygons.length === 0) {
      console.warn('[mergeShadowPolygonsClipper2] No valid polygons to merge');
      return [];
    }

    console.log(`[mergeShadowPolygonsClipper2] Converting ${martinezPolygons.length} polygons for union`);

    // Perform union operation using specialized shadow merging with interior preservation
    const unionResult = await unionShadowsWithInteriorPreservation(...martinezPolygons);

    console.log('[mergeShadowPolygonsClipper2] Union result:', {
      result: unionResult,
      resultType: typeof unionResult,
      isArray: Array.isArray(unionResult),
      length: unionResult?.length || 0
    });

    if (!unionResult || unionResult.length === 0) {
      console.warn('[mergeShadowPolygonsClipper2] Union operation returned empty result');
      return shadowArray; // Return original shadows as fallback
    }

    // Convert result back to shadow format
    const resultShadows = [];

    unionResult.forEach((polygon, polygonIndex) => {
      console.log(`[mergeShadowPolygonsClipper2] Processing union result ${polygonIndex}:`, {
        polygon: polygon,
        hasPolygon: !!polygon,
        polygonLength: polygon?.length || 0,
        firstRing: polygon?.[0],
        firstRingLength: polygon?.[0]?.length || 0
      });

      if (polygon && polygon.length > 0 && polygon[0] && polygon[0].length >= 3) {
        // Convert back from Martinez format [lng, lat] to Leaflet format [lat, lng]
        const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);

        console.log(`[mergeShadowPolygonsClipper2] Converted polygon ${polygonIndex} back to Leaflet format:`, {
          originalFirst3: polygon[0].slice(0, 3),
          leafletFirst3: leafletCoords.slice(0, 3),
          coordCount: leafletCoords.length
        });

        resultShadows.push({
          id: `merged_shadow_clipper2_${Date.now()}_${polygonIndex}`,
          coordinates: leafletCoords,
          type: 'aggregated'
        });
      } else {
        console.warn(`[mergeShadowPolygonsClipper2] Skipping invalid polygon ${polygonIndex}:`, polygon);
      }
    });

    console.log(`[mergeShadowPolygonsClipper2] Successfully merged ${shadowArray.length} input shadows into ${resultShadows.length} output shadows with interior preservation`);
    console.log('[mergeShadowPolygonsClipper2] Final result shadows:', resultShadows.map(s => ({
      id: s.id,
      coordCount: s.coordinates.length,
      type: s.type
    })));
    return resultShadows;

  } catch (error) {
    console.error('[mergeShadowPolygonsClipper2] Error merging shadows:', error);
    console.log('[mergeShadowPolygonsClipper2] Falling back to individual shadows');
    return shadowArray; // Return original shadows as fallback
  }
};

/**
 * Fallback function that uses the original Martinez-based merging
 * This is used when Clipper2 is not available or fails
 */
export const mergeShadowPolygonsFallback = (shadowArray) => {
  console.log(`[mergeShadowPolygonsFallback] Using fallback for ${shadowArray.length} shadows`);

  if (!shadowArray || shadowArray.length === 0) {
    return [];
  }

  // For now, just return the original shadows with proper type
  // This ensures shadows are still displayed even if merging fails
  const fallbackShadows = shadowArray.map((shadow, index) => ({
    ...shadow,
    id: shadow.id || `fallback_shadow_${Date.now()}_${index}`,
    type: 'aggregated' // Ensure proper type for rendering
  }));

  console.log(`[mergeShadowPolygonsFallback] Returning ${fallbackShadows.length} individual shadows as fallback`);
  return fallbackShadows;
};

/**
 * Post-processing step to identify and remove enclosed areas from merged shadow polygons
 * This fixes the issue where interior spaces are incorrectly shaded due to overlapping shadows
 * @param {Array} mergedShadows - Array of merged shadow objects with coordinates in Leaflet format
 * @param {Array} originalShadows - Array of original individual shadow objects for reference
 * @returns {Array} Shadow polygons with enclosed areas removed
 */
const removeEnclosedAreasPostProcessing = async (mergedShadows, originalShadows) => {
  console.log(`[removeEnclosedAreasPostProcessing] Processing ${mergedShadows.length} merged shadows to remove enclosed areas`);

  if (!mergedShadows || mergedShadows.length === 0) {
    return mergedShadows;
  }

  try {
    const processedShadows = [];

    for (let i = 0; i < mergedShadows.length; i++) {
      const mergedShadow = mergedShadows[i];
      console.log(`[removeEnclosedAreasPostProcessing] Processing merged shadow ${i} with ${mergedShadow.coordinates?.length || 0} coordinates`);

      if (!mergedShadow.coordinates || mergedShadow.coordinates.length < 3) {
        console.warn(`[removeEnclosedAreasPostProcessing] Skipping invalid shadow ${i}`);
        continue;
      }

      // Convert shadow coordinates to Martinez format for processing
      const shadowCoords = mergedShadow.coordinates.map(coord => [coord[1], coord[0]]); // [lat, lng] -> [lng, lat]

      // Ensure polygon is closed
      if (shadowCoords.length > 0) {
        const first = shadowCoords[0];
        const last = shadowCoords[shadowCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          shadowCoords.push([first[0], first[1]]);
        }
      }

      const martinezPolygon = [shadowCoords];

      // Identify potential enclosed areas within this merged shadow
      const enclosedAreas = await identifyEnclosedAreas(martinezPolygon, originalShadows);

      if (enclosedAreas.length > 0) {
        console.log(`[removeEnclosedAreasPostProcessing] Found ${enclosedAreas.length} enclosed areas to remove from shadow ${i}`);

        // Use difference operation to cut out enclosed areas
        let processedPolygon = martinezPolygon;

        for (const enclosedArea of enclosedAreas) {
          try {
            // Use Martinez for the difference operation as it's more reliable for this use case
            const differenceResult = martinez.difference([processedPolygon], [enclosedArea]);

            if (differenceResult && differenceResult.length > 0) {
              // Take the largest resulting polygon (main shadow area)
              let largestPolygon = differenceResult[0];
              let largestArea = calculatePolygonArea(largestPolygon[0]);

              for (let j = 1; j < differenceResult.length; j++) {
                const area = calculatePolygonArea(differenceResult[j][0]);
                if (area > largestArea) {
                  largestPolygon = differenceResult[j];
                  largestArea = area;
                }
              }

              processedPolygon = largestPolygon;
              console.log(`[removeEnclosedAreasPostProcessing] Successfully removed enclosed area, remaining area: ${largestArea.toFixed(2)} m²`);
            }
          } catch (differenceError) {
            console.warn(`[removeEnclosedAreasPostProcessing] Error removing enclosed area:`, differenceError);
          }
        }

        // Convert back to Leaflet format
        if (processedPolygon && processedPolygon.length > 0 && processedPolygon[0].length >= 3) {
          const leafletCoords = processedPolygon[0].map(coord => [coord[1], coord[0]]); // [lng, lat] -> [lat, lng]
          processedShadows.push({
            ...mergedShadow,
            id: `processed_${mergedShadow.id}`,
            coordinates: leafletCoords
          });
        }
      } else {
        // No enclosed areas found, keep original shadow
        processedShadows.push(mergedShadow);
      }
    }

    console.log(`[removeEnclosedAreasPostProcessing] Processed ${mergedShadows.length} shadows, returning ${processedShadows.length} shadows`);
    return processedShadows;

  } catch (error) {
    console.error('[removeEnclosedAreasPostProcessing] Error in post-processing:', error);
    // Return original shadows on error
    return mergedShadows;
  }
};

/**
 * Smart shadow merging that tries Clipper2 first, then falls back to Martinez
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons
 */
export const smartMergeShadowPolygons = async (shadowArray) => {
  console.log('[smartMergeShadowPolygons] Starting smart shadow merging with enclosed area removal...');

  try {
    // Try Clipper2 first
    console.log('[smartMergeShadowPolygons] Attempting Clipper2 merge...');
    const clipper2Result = await mergeShadowPolygonsClipper2(shadowArray);

    // Validate the result
    if (clipper2Result && clipper2Result.length > 0) {
      console.log('[smartMergeShadowPolygons] Clipper2 merge successful, applying post-processing...');

      // Apply post-processing to remove enclosed areas
      const postProcessedResult = await removeEnclosedAreasPostProcessing(clipper2Result, shadowArray);

      console.log(`[smartMergeShadowPolygons] Post-processing complete: ${clipper2Result.length} -> ${postProcessedResult.length} shadows`);
      return postProcessedResult;
    } else {
      console.warn('[smartMergeShadowPolygons] Clipper2 returned empty result, using fallback');
      const fallbackResult = mergeShadowPolygonsFallback(shadowArray);

      // Apply post-processing to fallback result as well
      if (fallbackResult && fallbackResult.length > 0) {
        console.log('[smartMergeShadowPolygons] Applying post-processing to fallback result...');
        return await removeEnclosedAreasPostProcessing(fallbackResult, shadowArray);
      }

      return fallbackResult;
    }
  } catch (error) {
    console.warn('[smartMergeShadowPolygons] Clipper2 merge failed, using fallback:', error);
    const fallbackResult = mergeShadowPolygonsFallback(shadowArray);

    // Apply post-processing to fallback result as well
    if (fallbackResult && fallbackResult.length > 0) {
      try {
        console.log('[smartMergeShadowPolygons] Applying post-processing to fallback result...');
        return await removeEnclosedAreasPostProcessing(fallbackResult, shadowArray);
      } catch (postProcessError) {
        console.warn('[smartMergeShadowPolygons] Post-processing failed on fallback, returning original:', postProcessError);
        return fallbackResult;
      }
    }

    return fallbackResult;
  }
};
