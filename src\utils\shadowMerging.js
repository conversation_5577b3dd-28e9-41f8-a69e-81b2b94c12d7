/**
 * Shadow Merging with <PERSON><PERSON>per2
 * 
 * Replaces Martinez-based shadow merging with Clipper2 WASM implementation
 * Provides better reliability and performance for polygon union operations
 */

import { unionPolygonsClipper2, differencePolygonsClipper2, testClipper2Operations } from './clipper2Operations';
import { isClipper2Initialized, safeInitializeClipper2 } from './wasmLoader';



/**
 * Calculate approximate polygon area using shoelace formula
 * @param {Array} coordinates - Array of [lng, lat] coordinates
 * @returns {number} Approximate area
 */
const calculatePolygonAreaApprox = (coordinates) => {
  if (!coordinates || coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  return Math.abs(area) / 2;
};

/**
 * Merge shadow polygons using Clipper2 WASM
 * @param {<PERSON>rray} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons
 */
export const mergeShadowPolygonsClipper2 = async (shadowArray) => {
  console.log(`[mergeShadowPolygonsClipper2] Starting merge of ${shadowArray.length} shadows`);
  console.log('[mergeShadowPolygonsClipper2] Input shadows:', shadowArray.map(s => ({
    id: s.id,
    coordCount: s.coordinates?.length || 0,
    type: s.type,
    hasCoords: !!s.coordinates
  })));

  if (!shadowArray || shadowArray.length === 0) {
    console.log('[mergeShadowPolygonsClipper2] No shadows to merge');
    return [];
  }

  if (shadowArray.length === 1) {
    console.log('[mergeShadowPolygonsClipper2] Only one shadow, returning as-is');
    return shadowArray;
  }

  try {
    // Ensure Clipper2 is initialized
    if (!isClipper2Initialized()) {
      console.log('[mergeShadowPolygonsClipper2] Initializing Clipper2...');
      const initResult = await safeInitializeClipper2();
      if (!initResult.success) {
        console.error('[mergeShadowPolygonsClipper2] Clipper2 initialization failed:', initResult.error);
        throw new Error(`Clipper2 initialization failed: ${initResult.error.message}`);
      }
    }

    // Convert shadow coordinates to Martinez format for Clipper2
    const martinezPolygons = shadowArray.map((shadow, index) => {
      if (!shadow.coordinates || shadow.coordinates.length === 0) {
        console.warn('[mergeShadowPolygonsClipper2] Shadow has no coordinates:', shadow);
        return null;
      }

      console.log(`[mergeShadowPolygonsClipper2] Converting shadow ${index} (${shadow.id}):`, {
        originalCoords: shadow.coordinates.slice(0, 3), // First 3 coords for debugging
        coordCount: shadow.coordinates.length
      });

      // Convert Leaflet coordinates [lat, lng] to Martinez format [lng, lat]
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);

      // Ensure the polygon is closed
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }

      const martinezPolygon = [martinezCoords]; // Martinez expects nested array format
      console.log(`[mergeShadowPolygonsClipper2] Converted shadow ${index} to Martinez format:`, {
        martinezCoords: martinezCoords.slice(0, 3), // First 3 coords for debugging
        coordCount: martinezCoords.length
      });

      return martinezPolygon;
    }).filter(polygon => polygon !== null);

    if (martinezPolygons.length === 0) {
      console.warn('[mergeShadowPolygonsClipper2] No valid polygons to merge');
      return [];
    }

    console.log(`[mergeShadowPolygonsClipper2] Converting ${martinezPolygons.length} polygons for union`);

    // Perform union operation using Clipper2
    const unionResult = await unionPolygonsClipper2(...martinezPolygons);

    console.log('[mergeShadowPolygonsClipper2] Union result:', {
      result: unionResult,
      resultType: typeof unionResult,
      isArray: Array.isArray(unionResult),
      length: unionResult?.length || 0
    });

    if (!unionResult || unionResult.length === 0) {
      console.warn('[mergeShadowPolygonsClipper2] Union operation returned empty result');
      return shadowArray; // Return original shadows as fallback
    }

    // Convert result back to shadow format
    const resultShadows = [];

    unionResult.forEach((polygon, polygonIndex) => {
      console.log(`[mergeShadowPolygonsClipper2] Processing union result ${polygonIndex}:`, {
        polygon: polygon,
        hasPolygon: !!polygon,
        polygonLength: polygon?.length || 0,
        firstRing: polygon?.[0],
        firstRingLength: polygon?.[0]?.length || 0
      });

      if (polygon && polygon.length > 0 && polygon[0] && polygon[0].length >= 3) {
        // Convert back from Martinez format [lng, lat] to Leaflet format [lat, lng]
        const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);

        console.log(`[mergeShadowPolygonsClipper2] Converted polygon ${polygonIndex} back to Leaflet format:`, {
          originalFirst3: polygon[0].slice(0, 3),
          leafletFirst3: leafletCoords.slice(0, 3),
          coordCount: leafletCoords.length
        });

        resultShadows.push({
          id: `merged_shadow_clipper2_${Date.now()}_${polygonIndex}`,
          coordinates: leafletCoords,
          type: 'aggregated'
        });
      } else {
        console.warn(`[mergeShadowPolygonsClipper2] Skipping invalid polygon ${polygonIndex}:`, polygon);
      }
    });

    console.log(`[mergeShadowPolygonsClipper2] Successfully merged ${shadowArray.length} input shadows into ${resultShadows.length} output shadows`);
    console.log('[mergeShadowPolygonsClipper2] Final result shadows:', resultShadows.map(s => ({
      id: s.id,
      coordCount: s.coordinates.length,
      type: s.type
    })));
    return resultShadows;

  } catch (error) {
    console.error('[mergeShadowPolygonsClipper2] Error merging shadows:', error);
    console.log('[mergeShadowPolygonsClipper2] Falling back to individual shadows');
    return shadowArray; // Return original shadows as fallback
  }
};

/**
 * Fallback function that uses the original Martinez-based merging
 * This is used when Clipper2 is not available or fails
 */
export const mergeShadowPolygonsFallback = (shadowArray) => {
  console.log(`[mergeShadowPolygonsFallback] Using fallback for ${shadowArray.length} shadows`);

  if (!shadowArray || shadowArray.length === 0) {
    return [];
  }

  // For now, just return the original shadows with proper type
  // This ensures shadows are still displayed even if merging fails
  const fallbackShadows = shadowArray.map((shadow, index) => ({
    ...shadow,
    id: shadow.id || `fallback_shadow_${Date.now()}_${index}`,
    type: 'aggregated' // Ensure proper type for rendering
  }));

  console.log(`[mergeShadowPolygonsFallback] Returning ${fallbackShadows.length} individual shadows as fallback`);
  return fallbackShadows;
};



/**
 * Advanced shadow merging with enclosed area detection and removal
 * This approach:
 * 1. Performs normal union merging
 * 2. Detects if the result has significantly more area than the sum of inputs
 * 3. If so, falls back to individual shadows to avoid enclosed areas
 * 4. Otherwise, returns the merged result
 *
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Intelligently merged shadow polygons
 */
export const advancedShadowMerging = async (shadowArray) => {
  console.log('[advancedShadowMerging] Starting advanced shadow merging with enclosed area detection...');

  if (!shadowArray || shadowArray.length === 0) {
    return [];
  }

  if (shadowArray.length === 1) {
    return shadowArray;
  }

  try {
    // Step 1: Calculate total area of individual shadows
    let totalIndividualArea = 0;
    const validShadows = [];

    for (const shadow of shadowArray) {
      if (!shadow.coordinates || shadow.coordinates.length < 4) {
        console.warn('[advancedShadowMerging] Skipping shadow with insufficient coordinates:', shadow);
        continue;
      }

      const area = calculatePolygonAreaApprox(shadow.coordinates.map(coord => [coord[1], coord[0]]));
      totalIndividualArea += area;
      validShadows.push(shadow);
    }

    if (validShadows.length === 0) {
      console.warn('[advancedShadowMerging] No valid shadows to merge');
      return [];
    }

    if (validShadows.length === 1) {
      return validShadows;
    }

    console.log(`[advancedShadowMerging] Total individual shadow area: ${totalIndividualArea.toFixed(6)}`);

    // Step 2: Attempt union merge
    const martinezPolygons = validShadows.map(shadow => {
      const martinezCoords = shadow.coordinates.map(coord => [coord[1], coord[0]]);
      // Ensure closed polygon
      if (martinezCoords.length > 0) {
        const first = martinezCoords[0];
        const last = martinezCoords[martinezCoords.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          martinezCoords.push([first[0], first[1]]);
        }
      }
      return [martinezCoords];
    });

    const unionResult = await unionPolygonsClipper2(...martinezPolygons);

    if (!unionResult || unionResult.length === 0) {
      console.warn('[advancedShadowMerging] Union returned empty result, keeping individual shadows');
      return validShadows;
    }

    // Step 3: Calculate area of union result
    let totalUnionArea = 0;
    for (const polygon of unionResult) {
      if (polygon && polygon[0] && polygon[0].length >= 3) {
        const area = calculatePolygonAreaApprox(polygon[0]);
        totalUnionArea += area;
      }
    }

    console.log(`[advancedShadowMerging] Union result area: ${totalUnionArea.toFixed(6)}`);

    // Step 4: Check if union area is reasonable
    const areaRatio = totalUnionArea / totalIndividualArea;
    console.log(`[advancedShadowMerging] Area ratio (union/individual): ${areaRatio.toFixed(3)}`);

    // If union area is significantly larger than individual areas,
    // it likely includes unwanted enclosed areas
    const AREA_THRESHOLD = 1.3; // Allow 30% increase due to overlaps

    if (areaRatio > AREA_THRESHOLD) {
      console.warn(`[advancedShadowMerging] Union area too large (${areaRatio.toFixed(2)}x), likely contains enclosed areas. Keeping individual shadows.`);

      // Return individual shadows with proper formatting
      const formattedShadows = validShadows.map((shadow, index) => ({
        ...shadow,
        id: shadow.id || `individual_shadow_${Date.now()}_${index}`,
        type: 'individual'
      }));

      return formattedShadows;
    }

    // Step 5: Union result is reasonable, convert back to shadow format
    const mergedShadows = unionResult.map((polygon, index) => {
      if (polygon && polygon[0] && polygon[0].length >= 3) {
        const leafletCoords = polygon[0].map(coord => [coord[1], coord[0]]);
        return {
          id: `advanced_merged_${Date.now()}_${index}`,
          coordinates: leafletCoords,
          type: 'aggregated'
        };
      }
      return null;
    }).filter(shadow => shadow !== null);

    console.log(`[advancedShadowMerging] Advanced merging successful: ${validShadows.length} → ${mergedShadows.length} shadows (area ratio: ${areaRatio.toFixed(2)})`);
    return mergedShadows;

  } catch (error) {
    console.error('[advancedShadowMerging] Advanced merging failed:', error);
    console.log('[advancedShadowMerging] Falling back to individual shadows');

    // Return individual shadows as fallback
    const formattedShadows = shadowArray.map((shadow, index) => ({
      ...shadow,
      id: shadow.id || `fallback_individual_${Date.now()}_${index}`,
      type: 'individual'
    }));

    return formattedShadows;
  }
};

/**
 * Smart shadow merging that uses advanced area-based detection to avoid enclosed areas
 * @param {Array} shadowArray - Array of shadow objects with coordinates
 * @returns {Array} Merged shadow polygons
 */
export const smartMergeShadowPolygons = async (shadowArray) => {
  console.log('[smartMergeShadowPolygons] Starting smart shadow merging with enclosed area detection...');

  try {
    // Use advanced merging approach that detects and avoids enclosed area problems
    console.log('[smartMergeShadowPolygons] Using advanced merging with area-based detection...');
    const advancedResult = await advancedShadowMerging(shadowArray);

    // Validate the result
    if (advancedResult && advancedResult.length > 0) {
      console.log('[smartMergeShadowPolygons] Advanced merging successful, returning result');
      return advancedResult;
    } else {
      console.warn('[smartMergeShadowPolygons] Advanced merging returned empty result, using fallback');
      return mergeShadowPolygonsFallback(shadowArray);
    }
  } catch (error) {
    console.warn('[smartMergeShadowPolygons] Advanced merging failed, using fallback:', error);
    return mergeShadowPolygonsFallback(shadowArray);
  }
};
