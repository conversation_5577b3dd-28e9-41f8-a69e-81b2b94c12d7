/**
 * Historical Imagery Cache Utility
 * Provides intelligent caching for historical satellite imagery tiles and metadata
 */

class HistoricalImageryCache {
  constructor(maxCacheSize = 100, maxAge = 30 * 60 * 1000) { // 30 minutes default
    this.cache = new Map();
    this.maxCacheSize = maxCacheSize;
    this.maxAge = maxAge;
    this.accessTimes = new Map();
  }

  /**
   * Generate cache key for wayback items
   */
  generateKey(type, params) {
    switch (type) {
      case 'wayback_items':
        return 'all_wayback_items';
      case 'local_changes':
        return `local_changes_${params.lat.toFixed(4)}_${params.lng.toFixed(4)}_${params.zoom}`;
      case 'tile_url':
        return `tile_url_${params.releaseNum}`;
      case 'metadata':
        return `metadata_${params.lat.toFixed(4)}_${params.lng.toFixed(4)}_${params.zoom}_${params.releaseNum}`;
      default:
        return `${type}_${JSON.stringify(params)}`;
    }
  }

  /**
   * Check if cache entry is valid (not expired)
   */
  isValid(key) {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    const now = Date.now();
    const age = now - entry.timestamp;
    return age < this.maxAge;
  }

  /**
   * Get cached data
   */
  get(key) {
    if (!this.isValid(key)) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      return null;
    }

    const entry = this.cache.get(key);
    this.accessTimes.set(key, Date.now());
    return entry.data;
  }

  /**
   * Set cached data
   */
  set(key, data) {
    // Clean up expired entries
    this.cleanup();

    // If cache is full, remove least recently used item
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLRU();
    }

    const entry = {
      data,
      timestamp: Date.now()
    };

    this.cache.set(key, entry);
    this.accessTimes.set(key, Date.now());
  }

  /**
   * Remove expired entries
   */
  cleanup() {
    const now = Date.now();
    const keysToDelete = [];

    for (const [key, entry] of this.cache.entries()) {
      const age = now - entry.timestamp;
      if (age >= this.maxAge) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      this.accessTimes.delete(key);
    });
  }

  /**
   * Evict least recently used item
   */
  evictLRU() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, accessTime] of this.accessTimes.entries()) {
      if (accessTime < oldestTime) {
        oldestTime = accessTime;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.accessTimes.delete(oldestKey);
    }
  }

  /**
   * Clear all cached data
   */
  clear() {
    this.cache.clear();
    this.accessTimes.clear();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
      entries: Array.from(this.cache.keys())
    };
  }

  /**
   * Preload wayback items for a location
   */
  async preloadForLocation(lat, lng, zoom = 15) {
    const key = this.generateKey('local_changes', { lat, lng, zoom });
    
    // Only preload if not already cached
    if (!this.isValid(key)) {
      try {
        const { getWaybackItemsWithLocalChanges } = await import('@vannizhang/wayback-core');
        const items = await getWaybackItemsWithLocalChanges(
          { latitude: lat, longitude: lng },
          zoom
        );
        
        const sortedItems = items.sort((a, b) => b.releaseDatetime - a.releaseDatetime);
        this.set(key, sortedItems);
        
        console.log(`[HistoricalImageryCache] Preloaded ${sortedItems.length} items for location ${lat}, ${lng}`);
        return sortedItems;
      } catch (error) {
        console.error('[HistoricalImageryCache] Preload failed:', error);
        return [];
      }
    }
    
    return this.get(key) || [];
  }

  /**
   * Batch preload multiple locations
   */
  async batchPreload(locations, zoom = 15) {
    const promises = locations.map(({ lat, lng }) => 
      this.preloadForLocation(lat, lng, zoom)
    );
    
    try {
      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      console.log(`[HistoricalImageryCache] Batch preload completed: ${successful}/${locations.length} successful`);
      return results;
    } catch (error) {
      console.error('[HistoricalImageryCache] Batch preload failed:', error);
      return [];
    }
  }
}

// Create singleton instance
const historicalImageryCache = new HistoricalImageryCache();

export default historicalImageryCache;

/**
 * Utility functions for cache management
 */
export const cacheUtils = {
  /**
   * Get cached wayback items
   */
  getCachedWaybackItems: () => {
    return historicalImageryCache.get('all_wayback_items');
  },

  /**
   * Cache wayback items
   */
  setCachedWaybackItems: (items) => {
    historicalImageryCache.set('all_wayback_items', items);
  },

  /**
   * Get cached local changes
   */
  getCachedLocalChanges: (lat, lng, zoom) => {
    const key = historicalImageryCache.generateKey('local_changes', { lat, lng, zoom });
    return historicalImageryCache.get(key);
  },

  /**
   * Cache local changes
   */
  setCachedLocalChanges: (lat, lng, zoom, items) => {
    const key = historicalImageryCache.generateKey('local_changes', { lat, lng, zoom });
    historicalImageryCache.set(key, items);
  },

  /**
   * Preload data for location
   */
  preloadLocation: (lat, lng, zoom) => {
    return historicalImageryCache.preloadForLocation(lat, lng, zoom);
  },

  /**
   * Clear cache
   */
  clearCache: () => {
    historicalImageryCache.clear();
  },

  /**
   * Get cache statistics
   */
  getCacheStats: () => {
    return historicalImageryCache.getStats();
  }
};
