// Main calculation logic for solar savings
import { fetchPVGIS } from './pvgis';
import { TARIFF_STRUCTURES, billToConsumption, calculateBill } from './tariffs';

/**
 * Group PV tables by their orientation (tilt + azimuth combination)
 * @param {Array} pvTables - Array of PV table objects
 * @returns {Array} Array of orientation groups with their tables and total peak power
 */
export function groupPVTablesByOrientation(pvTables) {
  if (!Array.isArray(pvTables) || pvTables.length === 0) {
    return [];
  }

  const orientationGroups = new Map();

  pvTables.forEach(table => {
    // Validate table structure
    if (!table || !table.properties) {
      console.warn('[groupPVTablesByOrientation] Invalid table structure:', table);
      return;
    }

    const props = table.properties;
    const tilt = Number(props.panelTilt) || 0;
    const azimuth = Number(props.azimuth) || 0;
    const modulesX = Number(props.modulesX) || 1;
    const modulesY = Number(props.modulesY) || 1;
    const modulePower = Number(props.modulePower) || 400; // Default 400W per module

    // Validate numeric values
    if (modulesX <= 0 || modulesY <= 0 || modulePower <= 0) {
      console.warn('[groupPVTablesByOrientation] Invalid table parameters:', {
        tableId: table.id,
        modulesX,
        modulesY,
        modulePower
      });
      return;
    }

    // Create orientation key (round to avoid floating point precision issues)
    const orientationKey = `${Math.round(tilt * 10) / 10}_${Math.round(azimuth * 10) / 10}`;

    // Calculate peak power for this table (in kW)
    const tablePeakPower = (modulesX * modulesY * modulePower) / 1000;

    if (!orientationGroups.has(orientationKey)) {
      orientationGroups.set(orientationKey, {
        tilt,
        azimuth,
        peakPower: 0,
        tableCount: 0,
        tables: []
      });
    }

    const group = orientationGroups.get(orientationKey);
    group.peakPower += tablePeakPower;
    group.tableCount += 1;
    group.tables.push({
      id: table.id,
      peakPower: tablePeakPower,
      modules: modulesX * modulesY
    });
  });

  return Array.from(orientationGroups.values());
}

/**
 * Calculate total peak power from PV tables
 * @param {Array} pvTables - Array of PV table objects
 * @returns {number} Total peak power in kW
 */
export function calculateTotalPeakPower(pvTables) {
  if (!Array.isArray(pvTables) || pvTables.length === 0) {
    return 0;
  }

  return pvTables.reduce((total, table) => {
    if (!table || !table.properties) return total;

    const props = table.properties;
    const modulesX = Number(props.modulesX) || 1;
    const modulesY = Number(props.modulesY) || 1;
    const modulePower = Number(props.modulePower) || 400;

    if (modulesX > 0 && modulesY > 0 && modulePower > 0) {
      return total + (modulesX * modulesY * modulePower) / 1000;
    }

    return total;
  }, 0);
}

/**
 * Validate PV table data for solar calculations
 * @param {Array} pvTables - Array of PV table objects
 * @returns {Object} Validation result with isValid flag and errors array
 */
export function validatePVTablesForCalculation(pvTables) {
  const result = {
    isValid: true,
    errors: [],
    warnings: []
  };

  if (!Array.isArray(pvTables)) {
    result.isValid = false;
    result.errors.push('PV tables data is not an array');
    return result;
  }

  if (pvTables.length === 0) {
    result.warnings.push('No PV tables found - will use default parameters');
    return result;
  }

  let validTableCount = 0;
  pvTables.forEach((table, index) => {
    if (!table) {
      result.warnings.push(`Table at index ${index} is null or undefined`);
      return;
    }

    if (!table.properties) {
      result.warnings.push(`Table ${table.id || index} has no properties`);
      return;
    }

    const props = table.properties;
    const modulesX = Number(props.modulesX);
    const modulesY = Number(props.modulesY);
    const modulePower = Number(props.modulePower);
    const tilt = Number(props.panelTilt);
    const azimuth = Number(props.azimuth);

    if (isNaN(modulesX) || modulesX <= 0) {
      result.warnings.push(`Table ${table.id || index} has invalid modulesX: ${props.modulesX}`);
      return;
    }

    if (isNaN(modulesY) || modulesY <= 0) {
      result.warnings.push(`Table ${table.id || index} has invalid modulesY: ${props.modulesY}`);
      return;
    }

    if (isNaN(modulePower) || modulePower <= 0) {
      result.warnings.push(`Table ${table.id || index} has invalid modulePower: ${props.modulePower}`);
      return;
    }

    if (isNaN(tilt) || tilt < 0 || tilt > 90) {
      result.warnings.push(`Table ${table.id || index} has invalid tilt: ${props.panelTilt}`);
      return;
    }

    if (isNaN(azimuth)) {
      result.warnings.push(`Table ${table.id || index} has invalid azimuth: ${props.azimuth}`);
      return;
    }

    validTableCount++;
  });

  if (validTableCount === 0) {
    result.isValid = false;
    result.errors.push('No valid PV tables found for calculation');
  }

  return result;
}

// Self-test functions (will be removed after testing)
if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost') {
  // Only run tests in development
  const testPVTables = [
    {
      id: 'test1',
      properties: {
        panelTilt: 25,
        azimuth: 0,
        modulesX: 4,
        modulesY: 6,
        modulePower: 400
      }
    },
    {
      id: 'test2',
      properties: {
        panelTilt: 25,
        azimuth: 0,
        modulesX: 3,
        modulesY: 5,
        modulePower: 400
      }
    },
    {
      id: 'test3',
      properties: {
        panelTilt: 30,
        azimuth: 45,
        modulesX: 2,
        modulesY: 4,
        modulePower: 500
      }
    }
  ];

  console.log('[PV Table Functions Test] Running self-tests...');

  const groups = groupPVTablesByOrientation(testPVTables);
  console.log('[PV Table Functions Test] Orientation groups:', groups);

  const totalPower = calculateTotalPeakPower(testPVTables);
  console.log('[PV Table Functions Test] Total peak power:', totalPower, 'kW');

  const validation = validatePVTablesForCalculation(testPVTables);
  console.log('[PV Table Functions Test] Validation result:', validation);
}

export async function calculateSolarSavings({ location, params, billing, pvTables = null }) {
  // 1. Prepare consumption array
  let yearlyConsumptions = [];
  const structure = billing.consumptionType === 'custom_tariff'
    ? { consumption_ranges_category: [1], consumption_ranges: [100000000], tariffs: [parseFloat(billing.customTariff) || 2.5] }
    : TARIFF_STRUCTURES[billing.consumptionType];

  if (billing.mode === 'average-consumption') {
    const avg = parseFloat(billing.fixedValue) || 0;
    yearlyConsumptions = Array(12).fill(avg);
  } else if (billing.mode === 'average-bill') {
    const avgBill = parseFloat(billing.fixedValue) || 0;
    const consumption = billToConsumption(avgBill, structure);
    yearlyConsumptions = Array(12).fill(consumption);
  } else if (billing.mode === 'variable-consumption' || billing.mode === 'variable-bill') {
    for (let i = 0; i < 12; i++) {
      let v = parseFloat(billing.monthlyValues[i]) || 0;
      if (billing.mode === 'variable-bill') {
        v = billToConsumption(v, structure);
      }
      yearlyConsumptions.push(v);
    }
  }

  // 2. Call PVGIS for monthly production
  let monthlyProduction = Array(12).fill(0);
  let totalSystemPeakPower = 0;

  // Check if PV tables are provided and valid
  const validation = validatePVTablesForCalculation(pvTables);

  if (validation.warnings.length > 0) {
    console.warn('[calculateSolarSavings] PV table validation warnings:', validation.warnings);
  }

  if (validation.errors.length > 0) {
    console.error('[calculateSolarSavings] PV table validation errors:', validation.errors);
    throw new Error(`Invalid PV table data: ${validation.errors.join(', ')}`);
  }

  if (pvTables && Array.isArray(pvTables) && pvTables.length > 0 && validation.isValid) {
    // Use PV table data - group by orientation and make multiple PVGIS calls
    console.log('[calculateSolarSavings] Using PV table data for calculations');

    const orientationGroups = groupPVTablesByOrientation(pvTables);
    console.log('[calculateSolarSavings] Found orientation groups:', orientationGroups);

    if (orientationGroups.length === 0) {
      throw new Error('No valid PV table orientations found for calculation');
    }

    // Make PVGIS calls for each orientation group
    const pvgisPromises = orientationGroups.map(async (group, index) => {
      try {
        console.log(`[calculateSolarSavings] Calling PVGIS for orientation ${index + 1}/${orientationGroups.length}: tilt=${group.tilt}°, azimuth=${group.azimuth}°, peakPower=${group.peakPower}kW`);

        const outputs = await fetchPVGIS({
          lat: location.lat,
          lng: location.lng,
          peakPower: group.peakPower,
          losses: params.losses,
          tilt: group.tilt,
          azimuth: group.azimuth,
          raddatabase: 'PVGIS-SARAH3',
        });

        return {
          group,
          monthlyProduction: outputs.E_m || Array(12).fill(0),
          success: true
        };
      } catch (error) {
        console.error(`[calculateSolarSavings] PVGIS call failed for orientation ${index + 1}:`, error);

        // Return zero production for failed calls but don't fail the entire calculation
        return {
          group,
          monthlyProduction: Array(12).fill(0),
          success: false,
          error: error.message
        };
      }
    });

    // Wait for all PVGIS calls to complete
    const pvgisResults = await Promise.all(pvgisPromises);

    // Check if any calls succeeded
    const successfulResults = pvgisResults.filter(result => result.success);
    const failedResults = pvgisResults.filter(result => !result.success);

    if (successfulResults.length === 0) {
      console.error('[calculateSolarSavings] All PVGIS calls failed, falling back to default parameters');

      // Fallback to default parameters if all PV table calls failed
      const outputs = await fetchPVGIS({
        lat: location.lat,
        lng: location.lng,
        peakPower: params.peakPower,
        losses: params.losses,
        tilt: params.tilt,
        azimuth: params.azimuth,
        raddatabase: 'PVGIS-SARAH3',
      });

      monthlyProduction = outputs.E_m || Array(12).fill(0);
      totalSystemPeakPower = params.peakPower;

      console.warn('[calculateSolarSavings] Used fallback parameters due to PV table API failures');
    } else {
      if (failedResults.length > 0) {
        console.warn(`[calculateSolarSavings] ${failedResults.length} out of ${pvgisResults.length} PVGIS calls failed:`,
          failedResults.map(r => r.error));
      }
    }

    // Combine monthly production from all orientations
    pvgisResults.forEach(result => {
      totalSystemPeakPower += result.group.peakPower;

      result.monthlyProduction.forEach((monthlyValue, monthIndex) => {
        monthlyProduction[monthIndex] += monthlyValue;
      });
    });

    console.log(`[calculateSolarSavings] Combined results: totalPeakPower=${totalSystemPeakPower}kW, yearlyProduction=${monthlyProduction.reduce((a, b) => a + b, 0)}kWh`);

  } else {
    // Fallback to default parameters
    console.log('[calculateSolarSavings] Using default parameters for calculations');

    const outputs = await fetchPVGIS({
      lat: location.lat,
      lng: location.lng,
      peakPower: params.peakPower,
      losses: params.losses,
      tilt: params.tilt,
      azimuth: params.azimuth,
      raddatabase: 'PVGIS-SARAH3',
    });

    monthlyProduction = outputs.E_m || Array(12).fill(0);
    totalSystemPeakPower = params.peakPower;
  }

  // 3. Calculate bills and savings
  const bills = [];
  const billTiers = [];
  const tariffRatesBefore = [];
  for (const consumption of yearlyConsumptions) {
    const [bill, tier] = calculateBill(structure, consumption);
    bills.push(bill);
    billTiers.push(tier);
    tariffRatesBefore.push(structure.tariffs[tier - 1] ?? null);
  }

  const newConsumptions = yearlyConsumptions.map((c, i) => Math.max(0, c - (monthlyProduction[i] || 0)));
  const newBills = [];
  const newBillTiers = [];
  const tariffRatesAfter = [];
  for (const consumption of newConsumptions) {
    const [bill, tier] = calculateBill(structure, consumption);
    newBills.push(bill);
    newBillTiers.push(tier);
    tariffRatesAfter.push(structure.tariffs[tier - 1] ?? null);
  }
  const savings = bills.map((b, i) => b - newBills[i]);
  const totalBill = bills.reduce((a, b) => a + b, 0);
  const totalNewBill = newBills.reduce((a, b) => a + b, 0);
  const savingsPercentage = totalBill > 0 ? ((totalBill - totalNewBill) / totalBill) * 100 : 0;

  return {
    yearlyConsumptions,
    monthlyProduction,
    newConsumptions,
    bills,
    newBills,
    savings,
    totalBill,
    totalNewBill,
    savingsPercentage,
    billTiers,
    newBillTiers,
    tariffRatesBefore,
    tariffRatesAfter,
  };
}
