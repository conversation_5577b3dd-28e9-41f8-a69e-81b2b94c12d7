/**
 * Test suite for Historical Imagery functionality
 * Tests the integration of ESRI World Imagery Wayback service
 */

import { renderHook, act } from '@testing-library/react';
import { useHistoricalImagery } from '../hooks/useHistoricalImagery';
import { cacheUtils } from '../utils/historicalImageryCache';

// Mock the wayback-core library
jest.mock('@vannizhang/wayback-core', () => ({
  getWaybackItems: jest.fn(),
  getWaybackItemsWithLocalChanges: jest.fn()
}));

describe('Historical Imagery Functionality', () => {
  const mockLocation = { lat: 40.7128, lng: -74.0060 }; // New York City
  
  const mockWaybackItems = [
    {
      itemID: 'test1',
      itemTitle: 'World Imagery (Wayback 2023-12-07)',
      itemURL: 'https://wayback.maptiles.arcgis.com/test/{releaseNum}/{level}/{row}/{col}',
      releaseNum: 56102,
      releaseDateLabel: '2023-12-07',
      releaseDatetime: 1701936000000
    },
    {
      itemID: 'test2',
      itemTitle: 'World Imagery (Wayback 2023-06-15)',
      itemURL: 'https://wayback.maptiles.arcgis.com/test/{releaseNum}/{level}/{row}/{col}',
      releaseNum: 55890,
      releaseDateLabel: '2023-06-15',
      releaseDatetime: 1686787200000
    }
  ];

  beforeEach(() => {
    // Clear cache before each test
    cacheUtils.clearCache();
    
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    const { getWaybackItems, getWaybackItemsWithLocalChanges } = require('@vannizhang/wayback-core');
    getWaybackItems.mockResolvedValue(mockWaybackItems);
    getWaybackItemsWithLocalChanges.mockResolvedValue(mockWaybackItems);
  });

  describe('useHistoricalImagery Hook', () => {
    test('should initialize with default state', () => {
      const { result } = renderHook(() =>
        useHistoricalImagery(mockLocation, false)
      );

      expect(result.current.waybackItems).toEqual([]);
      expect(result.current.availableItems).toEqual([]);
      expect(result.current.selectedItem).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.isHistoricalMode).toBe(false);
    });

    test('should automatically enable historical mode when item is selected', () => {
      const { result } = renderHook(() =>
        useHistoricalImagery(mockLocation, true)
      );

      act(() => {
        result.current.selectWaybackItem(mockWaybackItems[0]);
      });

      expect(result.current.selectedItem).toEqual(mockWaybackItems[0]);
      expect(result.current.isHistoricalMode).toBe(true);
    });

    test('should handle navigation from current to historical imagery', async () => {
      const { result } = renderHook(() =>
        useHistoricalImagery(mockLocation, true)
      );

      // Load items first
      await act(async () => {
        await result.current.loadWaybackItems();
      });

      // Initially no item selected (current imagery)
      expect(result.current.selectedItem).toBeNull();
      expect(result.current.isHistoricalMode).toBe(false);

      // Select first historical item
      act(() => {
        result.current.selectWaybackItem(mockWaybackItems[0]);
      });

      expect(result.current.selectedItem).toEqual(mockWaybackItems[0]);
      expect(result.current.isHistoricalMode).toBe(true);
    });

    test('should load wayback items when enabled', async () => {
      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      await act(async () => {
        await result.current.loadWaybackItems();
      });

      expect(result.current.waybackItems).toHaveLength(2);
      expect(result.current.waybackItems[0].releaseDateLabel).toBe('2023-12-07');
    });

    test('should load local changes for location', async () => {
      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      await act(async () => {
        await result.current.loadLocalChanges(mockLocation.lat, mockLocation.lng);
      });

      expect(result.current.availableItems).toHaveLength(2);
    });

    test('should select wayback item correctly', () => {
      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      act(() => {
        result.current.selectWaybackItem(mockWaybackItems[0]);
      });

      expect(result.current.selectedItem).toEqual(mockWaybackItems[0]);
      expect(result.current.isHistoricalMode).toBe(true);
    });

    test('should reset to current imagery', () => {
      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      // First select an item
      act(() => {
        result.current.selectWaybackItem(mockWaybackItems[0]);
      });

      // Then reset
      act(() => {
        result.current.resetToCurrent();
      });

      expect(result.current.selectedItem).toBeNull();
      expect(result.current.isHistoricalMode).toBe(false);
    });

    test('should generate correct tile URL', () => {
      const { result } = renderHook(() =>
        useHistoricalImagery(mockLocation, true)
      );

      act(() => {
        result.current.selectWaybackItem(mockWaybackItems[0]);
      });

      const tileUrl = result.current.getTileUrl();
      expect(tileUrl).toBe('https://wayback.maptiles.arcgis.com/test/56102/{z}/{y}/{x}');
    });

    test('should format dates correctly', () => {
      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      const formattedDate = result.current.formatDate(1701936000000);
      expect(formattedDate).toBe('Dec 7, 2023');
    });

    test('should calculate date range correctly', async () => {
      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      await act(async () => {
        await result.current.loadWaybackItems();
      });

      const dateRange = result.current.dateRange;
      expect(dateRange).toBeDefined();
      expect(dateRange.count).toBe(2);
      expect(dateRange.earliest).toBe(1686787200000);
      expect(dateRange.latest).toBe(1701936000000);
    });
  });

  describe('Cache Functionality', () => {
    test('should cache wayback items', () => {
      const testItems = [mockWaybackItems[0]];
      
      cacheUtils.setCachedWaybackItems(testItems);
      const cachedItems = cacheUtils.getCachedWaybackItems();
      
      expect(cachedItems).toEqual(testItems);
    });

    test('should cache local changes', () => {
      const lat = 40.7128;
      const lng = -74.0060;
      const zoom = 15;
      
      cacheUtils.setCachedLocalChanges(lat, lng, zoom, mockWaybackItems);
      const cachedItems = cacheUtils.getCachedLocalChanges(lat, lng, zoom);
      
      expect(cachedItems).toEqual(mockWaybackItems);
    });

    test('should provide cache statistics', () => {
      cacheUtils.setCachedWaybackItems(mockWaybackItems);
      
      const stats = cacheUtils.getCacheStats();
      expect(stats.size).toBeGreaterThan(0);
      expect(stats.maxSize).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      const { getWaybackItems } = require('@vannizhang/wayback-core');
      getWaybackItems.mockRejectedValue(new Error('API Error'));

      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      await act(async () => {
        await result.current.loadWaybackItems();
      });

      expect(result.current.error).toContain('Failed to load historical imagery');
      expect(result.current.isLoading).toBe(false);
    });

    test('should handle network timeouts', async () => {
      const { getWaybackItemsWithLocalChanges } = require('@vannizhang/wayback-core');
      getWaybackItemsWithLocalChanges.mockRejectedValue(new Error('Network timeout'));

      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      await act(async () => {
        await result.current.loadLocalChanges(mockLocation.lat, mockLocation.lng);
      });

      expect(result.current.error).toContain('Failed to load location-specific imagery');
    });
  });

  describe('Performance Optimizations', () => {
    test('should debounce location changes', async () => {
      const { result, rerender } = renderHook(
        ({ location }) => useHistoricalImagery(location, true),
        { initialProps: { location: mockLocation } }
      );

      // Change location multiple times quickly
      const newLocation1 = { lat: 40.7129, lng: -74.0061 };
      const newLocation2 = { lat: 40.7130, lng: -74.0062 };
      
      rerender({ location: newLocation1 });
      rerender({ location: newLocation2 });

      // Should only make one API call after debounce period
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 600)); // Wait for debounce
      });

      const { getWaybackItemsWithLocalChanges } = require('@vannizhang/wayback-core');
      expect(getWaybackItemsWithLocalChanges).toHaveBeenCalledTimes(1);
    });

    test('should use cached data when available', async () => {
      // Pre-populate cache
      cacheUtils.setCachedWaybackItems(mockWaybackItems);

      const { result } = renderHook(() => 
        useHistoricalImagery(mockLocation, true)
      );

      await act(async () => {
        await result.current.loadWaybackItems();
      });

      // Should not call API if data is cached
      const { getWaybackItems } = require('@vannizhang/wayback-core');
      expect(getWaybackItems).not.toHaveBeenCalled();
      expect(result.current.waybackItems).toEqual(mockWaybackItems);
    });
  });
});
