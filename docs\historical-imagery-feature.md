# Historical Satellite Imagery Feature

## Overview

The Historical Satellite Imagery feature allows users to view different time periods of satellite imagery for the same location, similar to Google Earth's timeline functionality. This feature helps users analyze how a building site has changed over time, which is valuable for solar installation planning by showing construction history, vegetation changes, and other site modifications.

## Implementation Details

### Architecture

The feature is built using ESRI's World Imagery Wayback service, which provides:
- **Free access** - No API keys required for basic tile access
- **Rich historical archive** - Over 150 versions spanning 8+ years
- **High quality imagery** - Same source as current ESRI providers
- **Metadata support** - Acquisition dates, resolution, provider info
- **Easy integration** - WMTS tile service compatible with Leaflet

### Key Components

#### 1. `useHistoricalImagery` Hook (`src/hooks/useHistoricalImagery.js`)
- Manages historical imagery state and API interactions
- Provides caching and performance optimizations
- Handles location-based imagery loading
- Debounces location changes to prevent excessive API calls

#### 2. `HistoricalImageryControl` Component (`src/components/map/HistoricalImageryControl.js`)
- User interface for selecting different time periods
- Time slider for easy navigation between dates
- Date range display and navigation controls
- Responsive design that doesn't interfere with main workflow

#### 3. `historicalImageryCache` Utility (`src/utils/historicalImageryCache.js`)
- Intelligent caching system with LRU eviction
- Prevents redundant API calls
- Configurable cache size and expiration
- Batch preloading capabilities

### Integration with OpenLayersMap

The feature is seamlessly integrated into the main map component:

```javascript
// Historical imagery state
const [historicalImageryEnabled, setHistoricalImageryEnabled] = useState(true);
const historicalImagery = useHistoricalImagery(location, historicalImageryEnabled);

// Dynamic TileLayer with historical support
<TileLayer
  key={historicalImagery.selectedItem?.releaseNum || 'current'}
  url={historicalImagery.isHistoricalMode && historicalImagery.selectedItem 
    ? historicalImagery.getTileUrl() 
    : SATELLITE_PROVIDER.url}
  // ... other props
/>
```

## Features

### 1. Simplified Arrow Navigation
- **Up Arrow (↑)**: Navigate to newer historical imagery dates
- **Down Arrow (↓)**: Navigate to older historical imagery dates
- **Smart Reset**: Up arrow returns to current imagery when at newest historical date
- **Visual Feedback**: Date overlay shows current selection, arrows disable at boundaries

### 2. Location-Aware Loading
- **Local Changes**: Loads imagery versions with changes for specific locations
- **Debounced Updates**: Prevents excessive API calls during map navigation
- **Automatic Loading**: Fetches historical data when location changes

### 3. Performance Optimizations
- **Intelligent Caching**: LRU cache with configurable size and expiration
- **Lazy Loading**: Only loads imagery when needed
- **Debounced Location Changes**: 500ms debounce to reduce API calls
- **Memory Management**: Automatic cleanup of expired cache entries

### 4. User Experience
- **Minimal UI**: Simple two-arrow navigation that doesn't disrupt workflow
- **Contextual Display**: Arrows only appear in satellite mode
- **Smart Behavior**: Automatic historical mode activation when navigating
- **Visual Feedback**: Date overlay and button states provide clear context

## Usage

### Basic Usage

1. **Enable Satellite Mode**: Click the satellite toggle button
2. **Navigate Historical Imagery**: Use the up/down arrow buttons that appear
   - **Down Arrow (↓)**: View older historical imagery
   - **Up Arrow (↑)**: View newer historical imagery or return to current
3. **Visual Feedback**: Date overlay shows current selection
4. **Return to Current**: Click up arrow when at newest historical date

### Advanced Features

#### Programmatic Control
```javascript
// Access historical imagery functionality
const historicalImagery = useHistoricalImagery(location, true);

// Load specific location data
await historicalImagery.loadLocalChanges(lat, lng, zoom);

// Select specific time period
historicalImagery.selectWaybackItem(waybackItem);

// Reset to current imagery
historicalImagery.resetToCurrent();
```

#### Cache Management
```javascript
import { cacheUtils } from '../utils/historicalImageryCache';

// Preload data for location
await cacheUtils.preloadLocation(lat, lng, zoom);

// Get cache statistics
const stats = cacheUtils.getCacheStats();

// Clear cache
cacheUtils.clearCache();
```

## API Reference

### useHistoricalImagery Hook

#### Parameters
- `location` (Object): Current map location `{lat, lng}`
- `enabled` (Boolean): Whether historical imagery is enabled

#### Returns
- `waybackItems` (Array): All available wayback items
- `availableItems` (Array): Location-specific wayback items
- `selectedItem` (Object): Currently selected wayback item
- `isLoading` (Boolean): Loading state
- `error` (String): Error message if any
- `isHistoricalMode` (Boolean): Whether in historical mode
- `loadWaybackItems` (Function): Load all wayback items
- `loadLocalChanges` (Function): Load location-specific items
- `selectWaybackItem` (Function): Select specific item
- `resetToCurrent` (Function): Reset to current imagery
- `getTileUrl` (Function): Get tile URL for selected item
- `formatDate` (Function): Format timestamp to readable date
- `dateRange` (Object): Available date range information

### HistoricalImageryControl Props

- `waybackItems` (Array): All available wayback items
- `availableItems` (Array): Location-specific items
- `selectedItem` (Object): Currently selected item
- `isLoading` (Boolean): Loading state
- `error` (String): Error message
- `isHistoricalMode` (Boolean): Historical mode state
- `onSelectItem` (Function): Item selection handler
- `onResetToCurrent` (Function): Reset handler
- `onToggleHistoricalMode` (Function): Mode toggle handler
- `formatDate` (Function): Date formatting function
- `dateRange` (Object): Date range information

## Compatibility

### Existing Features
The historical imagery feature is fully compatible with:
- ✅ PV table placement and editing
- ✅ Shadow analysis calculations
- ✅ Drawing tools and polygon creation
- ✅ Map navigation and zoom
- ✅ Theme switching (light/dark mode)
- ✅ Mobile and touch interactions

### Browser Support
- Modern browsers with ES6+ support
- Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- Mobile browsers on iOS 12+ and Android 7+

## Performance Considerations

### Optimization Strategies
1. **Caching**: Intelligent LRU cache reduces API calls
2. **Debouncing**: Location changes debounced to 500ms
3. **Lazy Loading**: Historical data loaded only when needed
4. **Memory Management**: Automatic cleanup of expired entries
5. **Progressive Loading**: Lower resolution previews (future enhancement)

### Network Usage
- Initial load: ~50KB for wayback items list
- Location-specific data: ~10-20KB per location
- Tile imagery: Standard map tile sizes (varies by zoom level)
- Cache reduces repeat requests by ~80-90%

## Testing

The feature includes comprehensive tests covering:
- Hook functionality and state management
- Cache operations and performance
- Error handling and edge cases
- Integration with map components
- Performance optimizations

Run tests with:
```bash
npm test -- historicalImagery.test.js
```

## Future Enhancements

### Planned Features
1. **Progressive Loading**: Lower resolution previews while loading
2. **Batch Operations**: Bulk download of historical imagery
3. **Comparison Mode**: Side-by-side view of different time periods
4. **Animation**: Time-lapse animation through historical periods
5. **Export**: Save historical imagery for offline use
6. **Metadata Display**: Show acquisition date, resolution, and provider info

### Alternative Providers
While ESRI Wayback is the primary provider, the architecture supports:
- Google Earth Engine (requires authentication)
- Planet Labs (commercial)
- Mapbox (limited historical data)
- Custom imagery services

## Troubleshooting

### Common Issues

1. **No Historical Data Available**
   - Some locations may have limited historical coverage
   - Try different zoom levels or nearby locations

2. **Slow Loading**
   - Check network connection
   - Clear cache if corrupted: `cacheUtils.clearCache()`

3. **API Errors**
   - ESRI service may be temporarily unavailable
   - Feature gracefully falls back to current imagery

4. **Memory Issues**
   - Reduce cache size in configuration
   - Clear cache periodically for long-running sessions

### Debug Information
Enable debug logging by setting:
```javascript
localStorage.setItem('debug', 'historical-imagery');
```

## License and Attribution

The historical imagery feature uses:
- **ESRI World Imagery Wayback**: Subject to ESRI terms of use
- **@vannizhang/wayback-core**: Apache 2.0 License
- **Imagery Attribution**: "Tiles © Esri — Source: Esri, Maxar, Earthstar Geographics, and the GIS User Community"

Proper attribution is automatically included in the map display.
