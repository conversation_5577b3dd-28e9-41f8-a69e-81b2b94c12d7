{"name": "solar-pv-react", "version": "1.0.0", "private": true, "dependencies": {"@tabler/icons-react": "^3.31.0", "@turf/area": "^7.2.0", "@turf/bbox": "^7.2.0", "@turf/boolean-contains": "^7.2.0", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/convex": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/turf": "^7.2.0", "@vannizhang/wayback-core": "^1.0.9", "chart.js": "^4.4.9", "clipper2-wasm": "^0.2.1", "dom-to-image": "^2.6.0", "html2canvas": "^1.4.1", "i18next": "^23.11.1", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-fullscreen": "^1.0.2", "leaflet-geometryutil": "^0.10.3", "martinez-polygon-clipping": "^0.7.4", "ol": "^10.5.0", "polygon-clipping": "^0.15.7", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-i18next": "^13.1.2", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-leaflet-draw": "^0.20.6", "react-scripts": "^5.0.1", "suncalc": "^1.9.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "3.4.4"}}