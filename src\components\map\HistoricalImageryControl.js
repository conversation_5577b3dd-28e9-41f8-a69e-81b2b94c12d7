import React, { useCallback, useMemo } from 'react';
import { FaChevronUp, FaChevronDown } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

/**
 * Simplified Historical Imagery Navigation Component
 * Provides two-arrow navigation for browsing historical satellite imagery
 */
const HistoricalImageryControl = ({
  waybackItems = [],
  availableItems = [],
  selectedItem,
  isLoading = false,
  error = null,
  onSelectItem,
  onResetToCurrent,
  formatDate
}) => {
  const { t } = useTranslation();

  // Use available items if present, otherwise use all wayback items
  const items = useMemo(() => {
    return availableItems.length > 0 ? availableItems : waybackItems;
  }, [availableItems, waybackItems]);

  // Find current selected index
  const selectedIndex = useMemo(() => {
    if (!selectedItem) return -1;
    return items.findIndex(item => item.releaseNum === selectedItem.releaseNum);
  }, [selectedItem, items]);

  // Check if we can navigate in each direction
  const canNavigateNewer = useMemo(() => {
    // Can navigate newer if we have a selected item and it's not the newest (index 0)
    return selectedIndex > 0;
  }, [selectedIndex]);

  const canNavigateOlder = useMemo(() => {
    // Can navigate older if:
    // 1. No item selected (selectedIndex === -1) and we have items available, OR
    // 2. We have a selected item and it's not the oldest (not at last index)
    return (selectedIndex === -1 && items.length > 0) ||
           (selectedIndex >= 0 && selectedIndex < items.length - 1);
  }, [selectedIndex, items.length]);

  // Check if we're viewing current imagery (no historical item selected)
  const isViewingCurrent = useMemo(() => {
    return selectedIndex === -1 || !selectedItem;
  }, [selectedIndex, selectedItem]);

  // Debug logging to track navigation state
  React.useEffect(() => {
    console.log('[HistoricalImageryControl] Navigation State:', {
      itemsCount: items.length,
      selectedIndex,
      selectedItemDate: selectedItem?.releaseDateLabel || 'none',
      canNavigateNewer,
      canNavigateOlder,
      isViewingCurrent,
      isLoading
    });
  }, [items.length, selectedIndex, selectedItem, canNavigateNewer, canNavigateOlder, isViewingCurrent, isLoading]);

  /**
   * Navigate to newer imagery (up arrow)
   */
  const navigateToNewer = useCallback(() => {
    console.log('[HistoricalImageryControl] navigateToNewer called:', {
      isLoading,
      canNavigateNewer,
      selectedIndex,
      itemsLength: items.length
    });

    if (isLoading) return;

    if (!canNavigateNewer) {
      // If we can't go newer and we're at the newest historical item, reset to current
      if (selectedIndex === 0) {
        console.log('[HistoricalImageryControl] Resetting to current imagery');
        onResetToCurrent();
        return;
      }
    }

    const newIndex = selectedIndex - 1; // Move to newer (lower index)
    const newItem = items[newIndex];

    console.log('[HistoricalImageryControl] Moving to newer item:', {
      newIndex,
      newItemDate: newItem?.releaseDateLabel
    });

    if (newItem) {
      onSelectItem(newItem);
    }
  }, [canNavigateNewer, isLoading, selectedIndex, items, onSelectItem, onResetToCurrent]);

  /**
   * Navigate to older imagery (down arrow)
   */
  const navigateToOlder = useCallback(() => {
    console.log('[HistoricalImageryControl] navigateToOlder called:', {
      canNavigateOlder,
      isLoading,
      selectedIndex,
      itemsLength: items.length
    });

    if (!canNavigateOlder || isLoading) return;

    if (selectedIndex === -1) {
      // No item selected, start with the newest (first item)
      console.log('[HistoricalImageryControl] Starting historical navigation with newest item');
      if (items.length > 0) {
        onSelectItem(items[0]);
      }
    } else {
      // Move to older (higher index)
      const newIndex = selectedIndex + 1;
      const newItem = items[newIndex];

      console.log('[HistoricalImageryControl] Moving to older item:', {
        newIndex,
        newItemDate: newItem?.releaseDateLabel
      });

      if (newItem) {
        onSelectItem(newItem);
      }
    }
  }, [canNavigateOlder, isLoading, selectedIndex, items, onSelectItem]);

  // Don't render if no items available
  if (items.length === 0 && !isLoading) {
    return null;
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
      {/* Up Arrow - Navigate to Newer Imagery */}
      <div className="leaflet-control leaflet-bar">
        <button
          type="button"
          onClick={navigateToNewer}
          disabled={isLoading}
          style={{
            width: '36px',
            height: '36px',
            background: selectedIndex === 0 ? 'rgba(37,99,235,0.15)' : 'var(--color-surface)', // Highlight when it will reset to current
            border: '2px solid rgba(0,0,0,0.2)',
            borderRadius: '4px',
            cursor: isLoading ? 'wait' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            color: (!canNavigateNewer && selectedIndex !== 0) || isLoading ? 'rgba(var(--color-text-rgb), 0.4)' : 'var(--color-text)',
            opacity: (!canNavigateNewer && selectedIndex !== 0) || isLoading ? 0.5 : 1,
            transition: 'opacity 0.2s ease, background 0.2s ease'
          }}
          title={
            isLoading
              ? 'Loading...'
              : canNavigateNewer
                ? `View newer imagery${selectedItem ? ` (before ${formatDate(selectedItem.releaseDatetime)})` : ''}`
                : selectedIndex === 0
                  ? 'Return to current imagery'
                  : 'No newer imagery available'
          }
        >
          <FaChevronUp size={14} />
        </button>
      </div>

      {/* Down Arrow - Navigate to Older Imagery */}
      <div className="leaflet-control leaflet-bar">
        <button
          type="button"
          onClick={navigateToOlder}
          disabled={!canNavigateOlder || isLoading}
          style={{
            width: '36px',
            height: '36px',
            background: 'var(--color-surface)',
            border: '2px solid rgba(0,0,0,0.2)',
            borderRadius: '4px',
            cursor: (!canNavigateOlder || isLoading) ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            color: (!canNavigateOlder || isLoading) ? 'rgba(var(--color-text-rgb), 0.4)' : 'var(--color-text)',
            opacity: (!canNavigateOlder || isLoading) ? 0.5 : 1,
            transition: 'opacity 0.2s ease'
          }}
          title={
            isLoading
              ? 'Loading historical imagery...'
              : !canNavigateOlder
                ? items.length === 0
                  ? 'No historical imagery available for this location'
                  : 'Already viewing oldest available imagery'
                : selectedItem
                  ? `View older imagery (after ${formatDate(selectedItem.releaseDatetime)})`
                  : `Start viewing historical imagery (${items.length} versions available)`
          }
        >
          <FaChevronDown size={14} />
        </button>
      </div>


      {/* Current date display overlay */}
      {(selectedItem || items.length > 0) && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50px',
            transform: 'translateY(-50%)',
            background: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '11px',
            fontWeight: '500',
            pointerEvents: 'none',
            zIndex: 1000,
            whiteSpace: 'nowrap'
          }}
        >
          {selectedItem ? formatDate(selectedItem.releaseDatetime) : 'Current'}
        </div>
      )}
    </div>
  );
};

export default HistoricalImageryControl;
